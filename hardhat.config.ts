import { HardhatUserConfig } from "hardhat/config";
// import "@nomicfoundation/hardhat-toolbox";
// import "@nomiclabs/hardhat-waffle";
import "@nomicfoundation/hardhat-ethers";
import "@nomicfoundation/hardhat-chai-matchers";
import "@nomiclabs/hardhat-etherscan";
import "@nomicfoundation/hardhat-network-helpers";
import "@openzeppelin/hardhat-upgrades";
import "@typechain/hardhat";
import "dotenv/config";
import { rpc } from "./config/rpc";
import { API_KEY } from "./config/api-key";

const getPK = (env?: string): string => {
  switch (env) {
    case "test":
      return process.env.TEST_KEY ?? "";
    case "dev":
      return process.env.DEV_KEY ?? "";
    case "prod":
      return process.env.PROD_KEY ?? "";
    case "new":
      return process.env.NEW_KEY ?? "";
    case "local":
      return process.env.LOCAL_KEY ?? "";
    default:
      return "";
  }
};

const config: HardhatUserConfig = {
  solidity: {
    version: "0.8.20",
    // settings: {
    //   optimizer: {
    //     enabled: false,
    //     runs: 200,
    //   }
    // }
  },
  networks: {
    amoy: {
      url: "https://polygon-amoy.infura.io/v3/********************************",
      chainId: 80002,
      accounts: [getPK(process.env.ENV)],
      gasPrice: **********
    },
    avaxFuji:{
      url:"https://api.avax-test.network/ext/bc/C/rpc",
      chainId: 43113,
      accounts: [getPK(process.env.ENV)],
      gasPrice: ************
    },
    bscMainnet: {
      url: rpc.BSC.mainnet[0],
      accounts: [getPK(process.env.ENV)],
      gasPrice: **********,
    },
    bscTestnet: {
      url: rpc.BSC.testnet[0],
      accounts: [getPK(process.env.ENV)],
    },
    polygon: {
      url: rpc.POLYGON.mainnet[0],
      accounts: [getPK(process.env.ENV)],
      gasPrice: ************,
    },
    mumbai: {
      url: rpc.POLYGON.testnet[0],
      accounts: [getPK(process.env.ENV)],
      // gasPrice: ***********,
    },
    eth: {
      url: rpc.ETH.mainnet[0],
      accounts: [getPK(process.env.ENV)],
      gasPrice: ***********,
    },
    goerli: {
      url: rpc.ETH.goerli[0],
      accounts: [getPK(process.env.ENV)],
      // gasPrice: **********,
    },
    sepolia: {
      url: rpc.ETH.sepolia[2],
      accounts: [getPK(process.env.ENV)],
    },
    meldMainnet: {
      url: rpc.MELD.mainnet[0],
      accounts: [getPK(process.env.ENV)],
    },
    meldTestnet: {
      url: rpc.MELD.testnet[0],
      accounts: [getPK(process.env.ENV)],
    },
    skaleTestnet: {
      url: rpc.SKALE.testnet[0],
      accounts: [getPK(process.env.ENV)],
    },
    localnet: {
      url: rpc.ETH.localnet[0],
      accounts: [getPK(process.env.ENV)],
    },
  },
  etherscan: {
    apiKey: {
      polygon: API_KEY.polygon,
      polygonMumbai: API_KEY.polygon,
      bsc: API_KEY.bsc,
      bscTestnet: API_KEY.bsc,
      mainnet: API_KEY.eth,
      goerli: API_KEY.eth,
      sepolia: API_KEY.eth,
      // meldTestnet: API_KEY.meld,
      // amoy: "b93a33a3-0ae5-4a58-b4aa-48a1d897e9c8",
      amoy: API_KEY.polygon,
      snowtrace: "snowtrace"
    },
    customChains: [
      {
          network: "amoy",
          chainId: 80002,
          urls: {
            apiURL: "https://api-amoy.polygonscan.com/api",
            browserURL: "https://amoy.polygonscan.com"
          }
      },
      {
        network: "snowtrace",
        chainId: 43113,
        urls: {
          apiURL: "https://api.routescan.io/v2/network/testnet/evm/43113/etherscan",
          browserURL: "https://avalanche.testnet.localhost:8080"
        }
      }
    ]
  },
};

export default config;
