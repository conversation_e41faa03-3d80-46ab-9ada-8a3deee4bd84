Resell for imported NFT 
    RoyaltyFee will be transfered to contract owner

+ Collection without owner 
    No signature 
    Cannot change collection setting(banner, royalty fee)
    No royaltyFee for transactions

+ Collection with owner (Ownable)
    Has signature
    Can can collection setting
    Has royaltyFee for transaction if set > 0

    *Owner(a)
        transfer(a)
    *Change owner a -> b
        transfer(a), only first time owner can change collection setting

resell(hasOwner, sellOrderSignature, collectionSignature)

// BuyNow
struct NftBuyRequest {
    uint256 tokenID; // NFT tokenID
    uint256 quantity; // NFT number of copies
    uint256 sellOrderSupply; // quantity for sell order
    uint256 sellOrderPrice; // sell order listing price
    uint256 enableMakeOffer; // offer mode status
    uint256 tokenType; // ERC1155: 0, ERC721: 1
    uint256 partnerType;
    uint256 partnerFee;
    uint256 transactionType;
    uint256 shippingFee;
    uint256 expiredAt;
    address creator; // NFT creator
    address receiver;
    address tokenAddress; // payment token address
    address contractAddress; // collection address of NFT
    bytes sellOrderSignature;
}

// acceptOffer
struct NftAcceptOffer {
    uint256 tokenID;
    uint256 quantity;
    uint256 sellOrderSupply;
    uint256 enableMakeOffer;
    uint256 sellOrderprice;
    uint256 offerAmount;
    uint256 offerPrice;
    uint256 listingTime;
    uint256 expirationTime;
    address creator;
    address contractAddress;
    address tokenAddress;
    address reciever;
    string sellOrderID;
}

// sell order
struct NftSellOrder {
    address creator;
    string sellOrderID;
}

// make order
struct NftMakeOffer {
    address creator;
    string offerID;
}

// resell
struct NftResell {
    uint256 tokenID;
    uint256 royaltyRatio;
    uint256 sellOrderSupply;
    uint256 sellOrderPrice
    uint256 enableMakeOffer;
    address creator;
    address contractAddress;
    address tokenAddress;
    string sellOrderID;
}

// Old Address
BuyerHandler: 0x69Ad773AC0A31BB6c581417bcAC48007d3d59FE8 , SignatureUtils: 0x36C0D9014E133adCE0B5d94D87aaA019841D0d05

// New Address
BuyerHandler: 0x6FbBB50Dd2c5D35A5Cd29FcFBc94594484AE5cB5 , SignatureUtils: 0x4A3b8d91c6fe333BC723d94662A1D45565823AA5

// TODO: check totalRatio = 10000

// BuyNowDataPayout
[101, "3", 1, "100000000000000000000", "1", 1, "0", 1000, "1000", 2000, "500", 500, "1000", 1000, "1000", 1000, "1000", 1000],["0x53789eA5850c5998B9121B816aAfC97f98a63E2A", "0x3a6eD8eaDEAE31180349E90Dd95BE6Cb1979cd0f",  "0xCA385e2B603F9b2E46f5699FD7E5e8c0aE020FBA", "0xCB4D8adf4ceb37e86300Edd196200f99F7a9B85d", "0xCB4D8adf4ceb37e86300Edd196200f99F7a9B85d", "0x75D46b9f6A8Ff92Da784bE6a763050f848551483", "0xe129Ea292EA5801a1Dc4d2664549c5Ca022E0Ff3", "0xbF3Aa5CbE3854fD3a9e0EB7bB385B3C5D453dBbf", "0x7DE3C64A15a023De119857c5025Cb6E725e7c0f3", "0xb64E3D636332E03437f7E2A04319e2Fee5564bb6", "0xc91E78540aBdF02399E8c6bf029024cA7213E83F", "0x14Ce166A10BF627651EbDB703e36297e9493e85c", "0x5f459594EA0C1787Ec6E6178B47Fe03B1F519113", "0xA781BC9Ef3Dc0D1E13f973264FF49531A1C84577", "0x5d7d41D2D4A198edE1cD0aa0bc3F31A413Aa8484"],[""],["0x8b99f71d5365e45cc96fb32955042a8aeb8b2c745bbebbc895dfe0c1eeaa87a422f61c2a2086cd15f3ed820a40afaff7bdd4b381a2af47f2febc39308dc5f9e61b", "0x97d68a520fbc71b82ad372852d2b7815128e5b7493840ea0c37548546bdddbcc6a7f25f9914f8015503d7c904d6e347883b476690aabd2afb1ca5e729d2a7cc11c", "0x9a211e60066f294bcc67648e9bb30c2f6e1c0ef4e7e611d8f51b7ff50e568f3f407dd2980c85bc3872f2baf55530ba6ac1eb6c66e9b6c043590c3beead51976b1c"]
[106,"3","5","100000000000000000000","1","1","0","1000", "1000", 2000, 7000],["0x53789eA5850c5998B9121B816aAfC97f98a63E2A","0x3a6eD8eaDEAE31180349E90Dd95BE6Cb1979cd0f","0xCA385e2B603F9b2E46f5699FD7E5e8c0aE020FBA","0xCB4D8adf4ceb37e86300Edd196200f99F7a9B85d","0xCB4D8adf4ceb37e86300Edd196200f99F7a9B85d","0xc91E78540aBdF02399E8c6bf029024cA7213E83F", "0x14Ce166A10BF627651EbDB703e36297e9493e85c", "0x5f459594EA0C1787Ec6E6178B47Fe03B1F519113"],[""],["0xb54de7a3768aad6dac4d617fa8d8751c44a560e5b2f634334d51e8ceef06639a34ed3e47f03d6dffdac299559b59347adb2ac06dc39403e38353e3e8c56ef0ca1b","0x97d68a520fbc71b82ad372852d2b7815128e5b7493840ea0c37548546bdddbcc6a7f25f9914f8015503d7c904d6e347883b476690aabd2afb1ca5e729d2a7cc11c","0x7e15427117c82ea45cae572f93961b9779b902a31bedf70add7233359f08761d78cc5e1f42868ce238313bb4a57dbddbf93426a8bb4f4955d6b17db4d51d2d481b"]
https://testnet.bscscan.com/tx/0xbed3cf8c54760fb12050dee77a0708031c03dc8aa1d6fc2e0bf50ae5a1b0639d 
=> GasUsed: 503,166 (10 Addrs), 339,372(5 Addrs), 290,164(3 Addrs) <> 189,789 (1 Addr) = No PayoutGroup


