[{"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "_tokenAddress", "type": "address"}, {"indexed": false, "internalType": "string", "name": "_currencyId", "type": "string"}], "name": "PaymentTokenEvent", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "_tokenAddress", "type": "address"}, {"indexed": false, "internalType": "string", "name": "_currencyId", "type": "string"}, {"indexed": false, "internalType": "uint256", "name": "_feeRate", "type": "uint256"}], "name": "ServiceFeeEvent", "type": "event"}, {"inputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}, {"internalType": "address[]", "name": "", "type": "address[]"}, {"internalType": "string[]", "name": "", "type": "string[]"}, {"internalType": "bytes[]", "name": "", "type": "bytes[]"}], "name": "acceptOffer", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "acceptedTokens", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "_currencyId", "type": "string"}, {"internalType": "address", "name": "_tokenAddress", "type": "address"}], "name": "addAcceptedToken", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "boxUtils", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "buyHandler", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}, {"internalType": "address[]", "name": "", "type": "address[]"}, {"internalType": "string[]", "name": "", "type": "string[]"}, {"internalType": "bytes[]", "name": "", "type": "bytes[]"}], "name": "buyNowNative", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [], "name": "cancelHandler", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}, {"internalType": "address[]", "name": "", "type": "address[]"}, {"internalType": "string[]", "name": "", "type": "string[]"}, {"internalType": "bytes[]", "name": "", "type": "bytes[]"}], "name": "cancelOffer", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}, {"internalType": "address[]", "name": "", "type": "address[]"}, {"internalType": "string[]", "name": "", "type": "string[]"}, {"internalType": "bytes[]", "name": "", "type": "bytes[]"}], "name": "cancelSaleOrder", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}, {"internalType": "address[]", "name": "", "type": "address[]"}, {"internalType": "bytes[]", "name": "", "type": "bytes[]"}, {"internalType": "bytes32", "name": "", "type": "bytes32"}, {"internalType": "uint8", "name": "", "type": "uint8"}, {"internalType": "bytes32", "name": "", "type": "bytes32"}, {"internalType": "bytes32", "name": "", "type": "bytes32"}], "name": "executeMetaTransaction", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "<PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "feeUtils", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_address", "type": "address"}], "name": "isAdmin", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_address", "type": "address"}, {"internalType": "uint8", "name": "_role", "type": "uint8"}], "name": "isSubAdmin", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "metaHandler", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "nonces", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "offerHandler", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}, {"internalType": "address[]", "name": "", "type": "address[]"}, {"internalType": "string[]", "name": "", "type": "string[]"}, {"internalType": "bytes[]", "name": "", "type": "bytes[]"}], "name": "openBox", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "", "type": "bytes"}], "name": "openedBoxSignatures", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "recipient", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_tokenAddress", "type": "address"}], "name": "removeAcceptedToken", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "sellHandler", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}, {"internalType": "address[]", "name": "", "type": "address[]"}, {"internalType": "string[]", "name": "", "type": "string[]"}, {"internalType": "bytes[]", "name": "", "type": "bytes[]"}], "name": "sellNowNative", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_address", "type": "address"}, {"internalType": "bool", "name": "value", "type": "bool"}], "name": "setAdminList", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newBoxUtils", "type": "address"}], "name": "setBoxUtils", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newBuyHandler", "type": "address"}], "name": "set<PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newCancelHandler", "type": "address"}], "name": "setCancelHandler", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newFeatureHandler", "type": "address"}], "name": "set<PERSON><PERSON><PERSON>Handler", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>ee<PERSON><PERSON><PERSON>", "type": "address"}], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newMetaHandler", "type": "address"}], "name": "setMetaHandler", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newOfferHandler", "type": "address"}], "name": "set<PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newRecipient", "type": "address"}], "name": "setRecipient", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newSellHandler", "type": "address"}], "name": "setSellHandler", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newSignatureUtils", "type": "address"}], "name": "setSignatureUtils", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_address", "type": "address"}, {"internalType": "bool", "name": "_value", "type": "bool"}], "name": "<PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_address", "type": "address"}, {"internalType": "uint8", "name": "_role", "type": "uint8"}, {"internalType": "bool", "name": "value", "type": "bool"}], "name": "setSubAdminList", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "string", "name": "_currencyId", "type": "string"}, {"internalType": "address", "name": "_tokenAddress", "type": "address"}, {"internalType": "uint256", "name": "_feeRate", "type": "uint256"}], "name": "setTokenFee", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "address"}], "name": "setTrusted<PERSON><PERSON><PERSON><PERSON>", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "signatureUtils", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "name": "soldQuantity", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes", "name": "", "type": "bytes"}], "name": "soldQuantityBySaleOrder", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "tokensFee", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "collection", "type": "address"}, {"internalType": "address", "name": "from", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}, {"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "uint256", "name": "tokenType", "type": "uint256"}, {"internalType": "bytes", "name": "data", "type": "bytes"}], "name": "transferNFT", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_to", "type": "address"}, {"internalType": "address", "name": "tokenAddress", "type": "address"}, {"internalType": "uint256", "name": "_amount", "type": "uint256"}], "name": "withdraw", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]