// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import {ERC1155} from "@openzeppelin/contracts/token/ERC1155/ERC1155.sol";
import {SignatureChecker} from "@openzeppelin/contracts/utils/cryptography/SignatureChecker.sol";
import "@openzeppelin/contracts/utils/cryptography/ECDSA.sol";

interface IERC1155Permit {
    function DOMAIN_SEPARATOR() external view returns (bytes32);

    /**
     * @dev Retrieve the current nonce of `account`
     * @param account the account's address
     * @return current account's nonce
     */
    function nonces(address account) external view returns (uint256);

    /**
     * @notice function to be called by anyone to approve `spender` using a Permit signature
     * @dev Anyone can call this to approve `spender`, even a third-party
     * @param owner the owner who approves
     * @param spender the actor to be approved
     * @param deadline the deadline for the permit to be used
     * @param v V component
     * @param r R component
     * @param s S component
     */
    function permit(address owner, address spender, uint256 deadline, uint8 v, bytes32 r, bytes32 s) external;
}
