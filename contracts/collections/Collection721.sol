// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import {Pausable} from "@openzeppelin/contracts/utils/Pausable.sol";
import {Ownable} from "@openzeppelin/contracts/access/Ownable.sol";
import {Strings} from "@openzeppelin/contracts/utils/Strings.sol";
import {ERC721Enumerable} from "@openzeppelin/contracts/token/ERC721/extensions/ERC721Enumerable.sol";
import "./ERC721Permit.sol";

contract Collection721 is Ownable, Pausable, ERC721Permit, ERC721Enumerable {
    using Strings for uint256;

    string public baseURI;
    address public controller;

    mapping(uint256 => string) public tokenURIs;

    constructor(string memory _name, string memory _symbol, string memory _uri, address _controller)
        ERC721(_name, _symbol) Ownable(_msgSender())
    {
        controller = _controller;
        baseURI = _uri;
    }

    /**
     * @dev See {ERC721-_update}
     */
    function _update(address to, uint256 tokenId, address auth)
        internal
        virtual
        override(ERC721, ERC721Enumerable)
        returns (address)
    {
        require(!paused(), "NFTify721: token transfer while paused");

        super._update(to, tokenId, auth);
    }

    /**
     * @dev See {ERC721-_baseURI}
     */
    function _baseURI() internal view virtual override returns (string memory) {
        return baseURI;
    }

    function setBaseURI(string memory newURI) public onlyOwner {
        baseURI = newURI;
    }

    /**
     * @dev Approve the controller for minting
     */
    function setController(address _newController) public onlyOwner {
        controller = _newController;
    }

    function setTokenURI(uint256 id, string memory uri) public {
        tokenURIs[id] = uri;
    }

    /**
     * @dev See {IERC721Metadata-tokenURI}.
     */
    function tokenURI(uint256 tokenId) public view virtual override returns (string memory) {
        _requireOwned(tokenId);

        string memory uri = _baseURI();
        return bytes(uri).length > 0 ? string(abi.encodePacked(uri, tokenId.toString())) : tokenURIs[tokenId];
    }

    /**
     * @dev Handle mint request
     *
     * Requirement:
     * - caller must be owner or controller
     *
     */
    function mint(address account, uint256 id, string memory uri) public {
        require(_msgSender() == controller || _msgSender() == owner(), "NFTify721: only owner or controller");
        tokenURIs[id] = uri;
        _safeMint(account, id, "");
    }

    /**
     * @dev Handle burn request
     *
     * Requirement:
     * - caller must be owner or approved
     *
     */
    function burn(uint256 id) public virtual {
        require(_isAuthorized(ownerOf(id), _msgSender(), id), "NFTify721: caller is not owner nor approved");
        _burn(id);
    }

    /**
     * @dev Pause all transfers in the contract
     */
    function pause() external virtual onlyOwner {
        _pause();
    }

    /**
     * @dev Unpause all transfers in the contract
     */
    function unpause() external virtual onlyOwner {
        _unpause();
    }

    /**
     * @dev See {ERC721Permit-supportsInterface, ERC721Enumerable-supportsInterface}
     */
    function supportsInterface(bytes4 interfaceId)
        public
        view
        virtual
        override(ERC721Enumerable, ERC721Permit)
        returns (bool)
    {
        return ERC721Enumerable.supportsInterface(interfaceId) || ERC721Permit.supportsInterface(interfaceId);
    }

    function _increaseBalance(address account, uint128 amount) internal virtual override(ERC721, ERC721Enumerable) {
        super._increaseBalance(account, amount);
    }
}
