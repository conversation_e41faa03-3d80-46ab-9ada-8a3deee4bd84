// collections/NFTify1155.sol

// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import {ERC1155Supply} from "@openzeppelin/contracts/token/ERC1155/extensions/ERC1155Supply.sol";
import {Ownable} from "@openzeppelin/contracts/access/Ownable.sol";
import {Pausable} from "@openzeppelin/contracts/utils/Pausable.sol";
import {Strings} from "@openzeppelin/contracts/utils/Strings.sol";

import "./ERC1155Permit.sol";

contract NFTify1155 is Ownable, Pausable, ERC1155Supply, ERC1155Permit {
    address public controller;

    event SetBaseUriChanged(string _newUri);

    constructor(string memory _name, string memory _symbol, string memory _uri, address _controller)
        ERC1155Permit(_name, _symbol, _uri)
        Ownable(_msgSender())
    {
        controller = _controller;
    }

    function _setURI(string memory newuri) internal virtual override {
        emit SetBaseUriChanged(newuri);
        super._setURI(newuri);
    }

    /**
     * @dev See {ERC1155-uri}
     */
    function uri(uint256 tokenId) public view virtual override returns (string memory) {
        string memory baseURI = super.uri(tokenId);
        return bytes(baseURI).length > 0 ? string(abi.encodePacked(baseURI, Strings.toString(tokenId))) : "";
    }

    /**
     * @dev Handle mint request
     */
    function mint(address account, uint256 id, uint256 amount, bytes memory data) public {
        require(_msgSender() == controller || _msgSender() == owner(), "NFTify1155: only owner or controller");
        _mint(account, id, amount, data);
    }

    /**
     * @dev Handle burn request
     */
    function burn(address account, uint256 id, uint256 value) public virtual {
        require(
            account == _msgSender() || isApprovedForAll(account, _msgSender()),
            "NFTify1155: caller is not owner nor approved"
        );

        _burn(account, id, value);
    }

    /**
     * @dev See {ERC1155Permit-supportsInterface}
     */
    function supportsInterface(bytes4 interfaceId)
        public
        view
        virtual
        override(ERC1155, ERC1155Permit)
        returns (bool)
    {
        return super.supportsInterface(interfaceId);
    }

    /**
     * @dev Approve the controller for minting
     */
    function setController(address _newController) public onlyOwner {
        controller = _newController;
    }

    /**
     * @dev Set the collection uri
     */
    function setURI(string memory _uri) public onlyOwner {
        _setURI(_uri);
    }

    /**
     * @dev Pause all transfers in the contract
     */
    function pause() external virtual onlyOwner {
        _pause();
    }

    /**
     * @dev Unpause all transfers in the contract
     */
    function unpause() external virtual onlyOwner {
        _unpause();
    }

    // overide
    /**
     * @dev See {ERC721-_update}
     */
    function _update(address from, address to, uint256[] memory ids, uint256[] memory values)
        internal
        virtual
        override(ERC1155, ERC1155Supply)
    {
        super._update(from, to, ids, values);
    }
}
