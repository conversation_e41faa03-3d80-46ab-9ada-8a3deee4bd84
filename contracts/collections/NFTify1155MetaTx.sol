// collections/NFTify1155.sol

// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import {ERC1155Supply} from "@openzeppelin/contracts/token/ERC1155/extensions/ERC1155Supply.sol";
import {Ownable} from "@openzeppelin/contracts/access/Ownable.sol";
import {Pausable} from "@openzeppelin/contracts/utils/Pausable.sol";
import {Strings} from "@openzeppelin/contracts/utils/Strings.sol";

import "./ERC1155Permit.sol";

contract NFTify1155 is Ownable, Pausable, ERC1155Supply, ERC1155Permit {
    address public controller;

    /*
     * Forwarder singleton we accept calls from
     */
    address private _trustedForwarder;

    constructor(
        string memory _name,
        string memory _symbol,
        string memory _uri,
        address _controller
    ) ERC1155Permit(_name, _symbol, _uri) Ownable(_msgSender()) {
        controller = _controller;
    }

    /**
     * @dev See {ERC1155-_update}.
     * replace for before/afterTokenTransfer in v4
     */
    function _update(address from, address to, uint256[] memory ids, uint256[] memory values) internal virtual override(ERC1155, ERC1155Supply) {
        require(!paused(), "NFTify: token transfer while paused");
        super._update(from, to, ids, values);
    }
    

    /**
     * @dev See {ERC1155-uri}
     */
    function uri(
        uint256 tokenId
    ) public view virtual override returns (string memory) {
        string memory baseURI = super.uri(tokenId);
        return
            bytes(baseURI).length > 0
                ? string(abi.encodePacked(baseURI, Strings.toString(tokenId)))
                : "";
    }

    /**
     * @dev Handle mint request
     */
    function mint(
        address account,
        uint256 id,
        uint256 amount,
        bytes memory data
    ) public {
        require(
            _msgSender() == controller || _msgSender() == owner(),
            "NFTify1155: only owner or controller"
        );
        _mint(account, id, amount, data);
    }

    /**
     * @dev Handle burn request
     */
    function burn(address account, uint256 id, uint256 value) public virtual {
        require(
            account == _msgSender() || isApprovedForAll(account, _msgSender()),
            "NFTify1155: caller is not owner nor approved"
        );

        _burn(account, id, value);
    }

    /**
     * @dev See {ERC1155Permit-supportsInterface}
     */
    function supportsInterface(
        bytes4 interfaceId
    ) public view virtual override(ERC1155, ERC1155Permit) returns (bool) {
        return super.supportsInterface(interfaceId);
    }

    /**
     * @dev Approve the controller for minting
     */
    function setController(address _newController) public onlyOwner {
        controller = _newController;
    }

    /**
     * @dev Set the collection uri
     */
    function setURI(string memory _uri) public onlyOwner {
        _setURI(_uri);
    }

    /**
     * @dev Pause all transfers in the contract
     */
    function pause() external virtual onlyOwner {
        _pause();
    }

    /**
     * @dev Unpause all transfers in the contract
     */
    function unpause() external virtual onlyOwner {
        _unpause();
    }

    /**
     * :warning: **Warning** :warning: The Forwarder can have a full control over your Recipient. Only trust verified Forwarder.
     * @notice Method is not a required method to allow Recipients to trust multiple Forwarders. Not recommended yet.
     * @return forwarder The address of the Forwarder contract that is being used.
     */
    function getTrustedForwarder()
        public
        view
        virtual
        returns (address forwarder)
    {
        return _trustedForwarder;
    }

    /**
     * @dev set forwarder address for gasless metadata transactions. See {ERC2771Context}
     *
     * Requirement:
     * - caller must be owner
     *
     */
    function setTrustedForwarder(address _forwarder) public onlyOwner {
        _trustedForwarder = _forwarder;
    }

    /**
     * @dev check if forwarder address is the verified forwarder.
     */
    function isTrustedForwarder(
        address forwarder
    ) public view virtual returns (bool) {
        return forwarder == _trustedForwarder;
    }

    /**
     * @dev See {ERC2771Context-_msgSender}
     */
    function _msgSender()
        internal
        view
        virtual
        override
        returns (address sender)
    {
        if (isTrustedForwarder(msg.sender)) {
            // The assembly code is more direct than the Solidity version using `abi.decode`.
            assembly {
                sender := shr(96, calldataload(sub(calldatasize(), 20)))
            }
        } else {
            return super._msgSender();
        }
    }

    /**
     * @dev See {ERC2771Context-_msgData}
     */
    function _msgData()
        internal
        view
        virtual
        override
        returns (bytes calldata)
    {
        if (isTrustedForwarder(msg.sender)) {
            return msg.data[:msg.data.length - 20];
        } else {
            return super._msgData();
        }
    }
}
