// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import {Pausable} from "@openzeppelin/contracts/utils/Pausable.sol";
import {Ownable} from "@openzeppelin/contracts/access/Ownable.sol";
import {ERC721Enumerable} from "@openzeppelin/contracts/token/ERC721/extensions/ERC721Enumerable.sol";
import {ERC2771Context} from "@openzeppelin/contracts/metatx/ERC2771Context.sol";

import "./ERC721Permit.sol";

contract NFTify721MetaTx is Ownable, Pausable, ERC721Permit, ERC721Enumerable {
    string public baseURI;
    address public controller;

    /*
     * Forwarder singleton we accept calls from
     */
    address private _trustedForwarder;

    constructor(string memory _name, string memory _symbol, string memory _uri, address _controller)
        ERC721(_name, _symbol)
        Ownable(_msgSender())
    {
        controller = _controller;
        baseURI = _uri;
    }


    /**
     * @dev See {ERC721-_update}
     * replace for before/afterTokenTransfer in v4
     */
    function _update(address to, uint256 tokenId, address auth)
        internal
        virtual
        override(ERC721, ERC721Enumerable)
        returns (address)
    {
        super._update(to, tokenId, auth);
        require(!paused(), "NFTify721: token transfer while paused");
    }

    /**
     * @dev See {ERC721-_baseURI}
     */
    function _baseURI() internal view virtual override returns (string memory) {
        return baseURI;
    }

    function setBaseURI(string memory newURI) public onlyOwner {
        baseURI = newURI;
    }

    /**
     * @dev Approve the controller for minting
     */
    function setController(address _newController) public onlyOwner {
        controller = _newController;
    }

    /**
     * @dev Handle mint request
     *
     * Requirement:
     * - caller must be owner or controller
     *
     */
    function mint(address account, uint256 id, bytes memory data) public {
        require(_msgSender() == controller || _msgSender() == owner(), "NFTify721: only owner or controller");
        _safeMint(account, id, data);
    }

    /**
     * @dev Handle burn request
     *
     * Requirement:
     * - caller must be owner or approved
     *
     */
    function burn(uint256 id) public virtual {
        require(_isAuthorized(ownerOf(id), _msgSender(), id), "NFTify721: caller is not owner nor approved");
        _burn(id);
    }

    /**
     * @dev Pause all transfers in the contract
     */
    function pause() external virtual onlyOwner {
        _pause();
    }

    /**
     * @dev Unpause all transfers in the contract
     */
    function unpause() external virtual onlyOwner {
        _unpause();
    }

    /**
     * @dev See {ERC721Permit-supportsInterface, ERC721Enumerable-supportsInterface}
     */
    function supportsInterface(bytes4 interfaceId)
        public
        view
        virtual
        override(ERC721Enumerable, ERC721Permit)
        returns (bool)
    {
        return ERC721Enumerable.supportsInterface(interfaceId) || ERC721Permit.supportsInterface(interfaceId);
    }

    /**
     * :warning: **Warning** :warning: The Forwarder can have a full control over your Recipient. Only trust verified Forwarder.
     * @notice Method is not a required method to allow Recipients to trust multiple Forwarders. Not recommended yet.
     * @return forwarder The address of the Forwarder contract that is being used.
     */
    function getTrustedForwarder() public view virtual returns (address forwarder) {
        return _trustedForwarder;
    }

    /**
     * @dev set forwarder address for gasless metadata transactions. See {ERC2771Context}
     *
     * Requirement:
     * - caller must be owner
     *
     */
    function setTrustedForwarder(address _forwarder) public onlyOwner {
        _trustedForwarder = _forwarder;
    }

    /**
     * @dev check if forwarder address is the verified forwarder.
     */
    function isTrustedForwarder(address forwarder) public view virtual returns (bool) {
        return forwarder == _trustedForwarder;
    }

    /**
     * @dev See {ERC2771Context-_msgSender}
     */
    function _msgSender() internal view virtual override returns (address sender) {
        if (isTrustedForwarder(msg.sender)) {
            // The assembly code is more direct than the Solidity version using `abi.decode`.
            assembly {
                sender := shr(96, calldataload(sub(calldatasize(), 20)))
            }
        } else {
            return super._msgSender();
        }
    }

    /**
     * @dev See {ERC2771Context-_msgData}
     */
    function _msgData() internal view virtual override returns (bytes calldata) {
        if (isTrustedForwarder(msg.sender)) {
            return msg.data[:msg.data.length - 20];
        } else {
            return super._msgData();
        }
    }

    // overide required
    function _increaseBalance(address account, uint128 value) internal virtual override(ERC721, ERC721Enumerable) {
        super._increaseBalance(account, value);
    }
}
