// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import {Pausable} from "@openzeppelin/contracts/utils/Pausable.sol";
import {Ownable} from "@openzeppelin/contracts/access/Ownable.sol";
import {ERC721Enumerable} from "@openzeppelin/contracts/token/ERC721/extensions/ERC721Enumerable.sol";

import "./ERC721Permit.sol";

contract NFTify721 is Ownable, Pausable, ERC721Permit, ERC721Enumerable {
    string public baseURI;
    address public controller;
    uint256 lockDuration;

    mapping(uint256 => uint256) public nftUnlockTime;

    event SetBaseUriChanged(string _newUri);

    constructor(
        string memory _name,
        string memory _symbol,
        string memory _uri,
        address _controller,
        uint256 _lockDuration
    ) ERC721(_name, _symbol) Ownable(_msgSender()) {
        controller = _controller;
        lockDuration = _lockDuration;
        setBaseURI(_uri);
    }

    /**
     * @dev See {ERC721-_update}
     * replace for before/afterTokenTransfer in v4
     */
    function _update(address to, uint256 tokenId, address auth)
        internal
        virtual
        override(ERC721, ERC721Enumerable)
        returns (address)
    {
        super._update(to, tokenId, auth);

        require(!paused(), "NFTify721: token transfer while paused");
        require(nftUnlockTime[tokenId] < block.timestamp, "NFTify721: token is locked");
    }

    /**
     * @dev See {ERC721-_baseURI}
     */
    function _baseURI() internal view virtual override returns (string memory) {
        return baseURI;
    }

    function setBaseURI(string memory newURI) public onlyOwner {
        emit SetBaseUriChanged(newURI);
        baseURI = newURI;
    }

    /**
     * @dev Approve the controller for minting
     */
    function setController(address _newController) public onlyOwner {
        controller = _newController;
    }

    /**
     * @dev Handle mint request
     *
     * Requirement:
     * - caller must be owner or controller
     *
     */
    function mint(address account, uint256 id, bytes memory data) public {
        require(_msgSender() == controller || _msgSender() == owner(), "NFTify721: only owner or controller");
        _safeMint(account, id, data);
        nftUnlockTime[id] = block.timestamp + lockDuration;
    }

    // function updateLockDuration(uint256 _lockDuration) public onlyOwner {
    //     lockDuration = _lockDuration;
    // }

    /**
     * @dev Handle burn request
     *
     * Requirement:
     * - caller must be owner or approved
     *
     */
    function burn(uint256 id) public virtual {
        require(_isAuthorized(ownerOf(id), _msgSender(), id), "NFTify721: caller is not owner nor approved");
        _burn(id);
    }

    /**
     * @dev Pause all transfers in the contract
     */
    function pause() external virtual onlyOwner {
        _pause();
    }

    /**
     * @dev Unpause all transfers in the contract
     */
    function unpause() external virtual onlyOwner {
        _unpause();
    }

    /**
     * @dev See {ERC721Permit-supportsInterface, ERC721Enumerable-supportsInterface}
     */
    function supportsInterface(bytes4 interfaceId)
        public
        view
        virtual
        override(ERC721Enumerable, ERC721Permit)
        returns (bool)
    {
        return ERC721Enumerable.supportsInterface(interfaceId) || ERC721Permit.supportsInterface(interfaceId);
    }

    // overide required
    function _increaseBalance(address account, uint128 value) internal virtual override(ERC721, ERC721Enumerable) {
        super._increaseBalance(account, value);
    }
}
