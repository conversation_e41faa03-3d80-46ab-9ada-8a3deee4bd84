// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import "@openzeppelin/contracts/token/ERC20/extensions/ERC20Permit.sol";

contract NFTify20 is ERC20Permit {
    constructor(string memory _name, string memory _symbol)
        ERC20Permit(_name)
        ERC20(_name, _symbol)
    {
        _mint(msg.sender, 10**6 * 10**18);
    }

    function mint() public {
        _mint(msg.sender, 10**6 * 10**18);
    }
}
