// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import {Pausable} from "@openzeppelin/contracts/utils/Pausable.sol";
import {Ownable} from "@openzeppelin/contracts/access/Ownable.sol";
import {ERC721Enumerable} from "@openzeppelin/contracts/token/ERC721/extensions/ERC721Enumerable.sol";
import {IERC721} from "@openzeppelin/contracts/token/ERC721/IERC721.sol";

import "./ERC721Permit.sol";

contract NFTify721SBT is Ownable, Pausable, ERC721Permit, ERC721Enumerable {
    string public baseURI;
    address public controller;
    uint256 lockDuration;

    mapping(uint256 => uint256) public nftUnlockTime;

    // Flag to allow burning in a controlled manner
    bool private _burnInProgress;

    event SetBaseUriChanged(string _newUri);

    constructor(
        string memory _name,
        string memory _symbol,
        string memory _uri,
        address _controller,
        uint256 _lockDuration
    ) ERC721(_name, _symbol) Ownable(_msgSender()) {
        controller = _controller;
        lockDuration = _lockDuration;
        setBaseURI(_uri);
    }

    /**
     * @dev Override transfer function to prevent token transfers (soulbound functionality)
     * This will block all transfers, including to the zero address (burning)
     */
    function transferFrom(address, address, uint256) public virtual override(ERC721, IERC721) {
        revert("NFTify721SBT: SBT tokens cannot be transferred");
    }

    /**
     * @dev See {ERC721-_update}
     * replace for before/afterTokenTransfer in v4
     * Allows minting and burning through proper functions, but prevents transfers
     */
    function _update(address to, uint256 tokenId, address auth)
        internal
        virtual
        override(ERC721, ERC721Enumerable)
        returns (address)
    {
        address from = _ownerOf(tokenId);
        
        // Allow minting (from = address(0)) and burning through burn function (_burnInProgress = true)
        // Block all direct transfers between addresses
        if (from != address(0) && !_burnInProgress) {
            revert("NFTify721SBT: SBT tokens cannot be transferred");
        }
        
        address updatedFrom = super._update(to, tokenId, auth);
        require(!paused(), "NFTify721SBT: token transfer while paused");
        return updatedFrom;
    }

    /**
     * @dev See {ERC721-_baseURI}
     */
    function _baseURI() internal view virtual override returns (string memory) {
        return baseURI;
    }

    function setBaseURI(string memory newURI) public onlyOwner {
        emit SetBaseUriChanged(newURI);
        baseURI = newURI;
    }

    /**
     * @dev Approve the controller for minting
     */
    function setController(address _newController) public onlyOwner {
        controller = _newController;
    }

    /**
     * @dev Handle mint request
     *
     * Requirement:
     * - caller must be owner or controller
     *
     */
    function mint(address account, uint256 id, bytes memory data) public {
        require(_msgSender() == controller || _msgSender() == owner(), "NFTify721SBT: only owner or controller");
        _safeMint(account, id, data);
        nftUnlockTime[id] = block.timestamp + lockDuration;
    }

    // function updateLockDuration(uint256 _lockDuration) public onlyOwner {
    //     lockDuration = _lockDuration;
    // }

    /**
     * @dev Handle burn request through direct _burn call
     *
     * Requirement:
     * - caller must be owner or approved
     *
     */
    function burn(uint256 id) public virtual {
        require(_isAuthorized(ownerOf(id), _msgSender(), id), "NFTify721SBT: caller is not owner nor approved");
        _burnInProgress = true;
        _burn(id);
        _burnInProgress = false;
    }

    /**
     * @dev Pause all transfers in the contract
     */
    function pause() external virtual onlyOwner {
        _pause();
    }

    /**
     * @dev Unpause all transfers in the contract
     */
    function unpause() external virtual onlyOwner {
        _unpause();
    }

    /**
     * @dev See {ERC721Permit-supportsInterface, ERC721Enumerable-supportsInterface}
     */
    function supportsInterface(bytes4 interfaceId)
        public
        view
        virtual
        override(ERC721Enumerable, ERC721Permit)
        returns (bool)
    {
        return ERC721Enumerable.supportsInterface(interfaceId) || ERC721Permit.supportsInterface(interfaceId);
    }

    // overide required
    function _increaseBalance(address account, uint128 value) internal virtual override(ERC721, ERC721Enumerable) {
        super._increaseBalance(account, value);
    }
}
