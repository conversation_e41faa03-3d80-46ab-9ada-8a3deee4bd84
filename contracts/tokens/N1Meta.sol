// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import {ERC20} from "@openzeppelin/contracts/token/ERC20/ERC20.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@gelatonetwork/relay-context/contracts/vendor/ERC2771Context.sol";

contract N1Meta is ERC20, ERC2771Context {
    constructor(address _forwarder) ERC20("NFTIFY-COIN", "N1") ERC2771Context(_forwarder) {
        _mint(msg.sender, 100000000 * 10 ** decimals());
    }

    function mint(address to, uint256 amount) public {
        _mint(to, amount);
    }

    function mint100() public {
        _mint(_msgSender(), 100 * 10 ** decimals());
    }

    function decimals() public pure override returns (uint8) {
        return 18;
    }

    function _msgSender() internal view virtual override(Context, ERC2771Context) returns (address sender) {
        return ERC2771Context._msgSender();
    }

    function _msgData() internal view virtual override(Context, ERC2771Context) returns (bytes calldata) {
        return ERC2771Context._msgData();
    }
}
