// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import {Upgradeable} from "../common/Upgradeable.sol";

contract NFTExchange is Upgradeable {
    event PaymentTokenEvent(address indexed _tokenAddress, string _currencyId);
    event ServiceFeeEvent(
        address indexed _tokenAddress,
        string _currencyId,
        uint256 _feeRate
    );

    event updateBurnRedeemHandler(address _newBurnRedeemHandler);
    event updateOfferHandler(address _newOfferHandler);
    event updateSignatureUtils(address _newSignatureUtils);
    event updateBuyHandler(address _newBuyHandler);
    event updateRecipient(address _newRecipient);
    event updateSubscribeHandler(address _newSubscribeHandler);
    event updateSellHandler(address _newSellHandler);
    event updateCancelHandler(address _newCancelHandler);
    event updateTrustedForwarder(address _newForwarder);
    event updateFeeUtils(address _newFeeUtils);
    event updateBoxUtils(address _newBoxUtils);
    event updateMetaHandler(address _newMetaHandler);

    modifier onlyAdmins() {
        require(adminList[msg.sender] || msg.sender == proxyOwner(), "Need admin role");
        _;
    }

    modifier onlyAdminsAndSubAdmins(uint8 _role) {
        require(
            adminList[msg.sender] || subAdminList[msg.sender][_role],
            "Only admins or sub-admin with this role"
        );
        _;
    }

    function proxyOwner() public view returns (address owner) {
        bytes32 position = bytes32(uint256(keccak256('eip1967.proxy.admin')) - 1);
        assembly {
            owner := sload(position)
        }
    }

    function isAdmin(address _address) public view returns (bool) {
        return adminList[_address];
    }

    function isSubAdmin(
        address _address,
        uint8 _role
    ) public view returns (bool) {
        return subAdminList[_address][_role] || adminList[_address];
    }

    function setBurnRedeemHandler(
        address newBurnRedeemHandler
    ) public onlyAdmins {
        burnRedeemHandler = newBurnRedeemHandler;
        emit updateBurnRedeemHandler(newBurnRedeemHandler);
    }

    function setOfferHandler(address newOfferHandler) public onlyAdmins {
        offerHandler = newOfferHandler;
        emit updateOfferHandler(newOfferHandler);
    }

    function setSignatureUtils(address newSignatureUtils) public onlyAdmins {
        signatureUtils = newSignatureUtils;
        emit updateSignatureUtils(newSignatureUtils);
    }

    function setBuyHandler(address newBuyHandler) public onlyAdmins {
        buyHandler = newBuyHandler;
        emit updateBuyHandler(newBuyHandler);
    }

    function setRecipient(address newRecipient) public onlyAdmins {
        recipient = newRecipient;
        emit updateRecipient(newRecipient);
    }

    function setSubscribeHandler(
        address newSubscribeHandler
    ) public onlyAdmins {
        subscribeHandler = newSubscribeHandler;
        emit updateSubscribeHandler(newSubscribeHandler);
    }

    function setSellHandler(address newSellHandler) public onlyAdmins {
        sellHandler = newSellHandler;
        emit updateSellHandler(newSellHandler);
    }

    function setCancelHandler(address newCancelHandler) public onlyAdmins {
        cancelHandler = newCancelHandler;
        emit updateCancelHandler(newCancelHandler);
    }

    function setTrustedForwarder(address newForwarder) public onlyAdmins {
        trustedForwarder = newForwarder;
        emit updateTrustedForwarder(newForwarder);
    }

    function setFeeUtils(address newFeeUtils) public onlyAdmins {
        feeUtils = newFeeUtils;
        emit updateFeeUtils(newFeeUtils);
    }

    function setBoxUtils(address newBoxUtils) public onlyAdmins {
        boxUtils = newBoxUtils;
        emit updateBoxUtils(newBoxUtils);
    }

    function setMetaHandler(address newMetaHandler) public onlyAdmins {
        metaHandler = newMetaHandler;
        emit updateMetaHandler(newMetaHandler);
    }

    function setAdminList(address _address, bool value) public {
        adminList[_address] = value;
    }

    function setSubAdminList(
        address _address,
        uint8 _role,
        bool value
    ) public onlyAdmins {
        subAdminList[_address][_role] = value;
    }

    function setSigner(address _address, bool _value) external onlyAdmins {
        signers[_address] = _value;
    }

    function setTokenFee(
        string memory _currencyId,
        address _tokenAddress,
        uint256 _feeRate
    ) public onlyAdminsAndSubAdmins(2) {
        tokensFee[_tokenAddress] = _feeRate;
        emit ServiceFeeEvent(_tokenAddress, _currencyId, _feeRate);
    }

    function addAcceptedToken(
        string memory _currencyId,
        address _tokenAddress
    ) public onlyAdminsAndSubAdmins(1) {
        acceptedTokens[_tokenAddress] = true;
        emit PaymentTokenEvent(_tokenAddress, _currencyId);
    }

    function removeAcceptedToken(
        address _tokenAddress
    ) public onlyAdminsAndSubAdmins(1) {
        acceptedTokens[_tokenAddress] = false;
    }

    function setAirdropHandler(address newAirdropHandler) public onlyAdmins {
        airdropHandler = newAirdropHandler;
    }

    function getNonce(
        bytes32 handler,
        address account
    ) public view returns (uint256) {
        return _nonces[handler][account];
    }
}
