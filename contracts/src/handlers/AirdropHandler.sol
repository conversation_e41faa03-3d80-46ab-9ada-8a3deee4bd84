// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import {ECDSA} from "@openzeppelin/contracts/utils/cryptography/ECDSA.sol";

import {Upgradeable} from "../common/Upgradeable.sol";
import {AssemblyUtils} from "../utils/AssemblyUtils.sol";
import {INFTify1155} from "../interfaces/INFTify1155.sol";
import {INFTify721} from "../interfaces/INFTify721.sol";
import {MessageHashUtils} from "@openzeppelin/contracts/utils/cryptography/MessageHashUtils.sol";

/**
 * receiver - The address who claims NFT
 * tokenIds - The list of NFT id
 * amounts - The amount of each NFT
 * types - The type of each NFT: 0 - 1155, 1 - 721
 * collections - The collection's address of each NFT
 * start - The event's start time
 * end - The event's end time
 * limitation - the maximum number of NFT each user can claim in each event
 * nonce - value for preventing replay attack
 * airdropEventSignature - the airdrop event's signature (signed by admin wallet)
 */
struct AirdropClaim {
    address receiver;
    uint256[] tokenIds;
    uint256[] amounts;
    uint256[] types;
    uint256[] quantities;
    address[] collections;
    uint256 start;
    uint256 end;
    uint256 limitation;
    uint256 nonce;
    bytes airdropEventSignature;
}

contract NFTAirdropHandler is Upgradeable {
    using AssemblyUtils for uint256;

    uint256 private constant ERC_1155 = 1;
    uint256 private constant ERC_721 = 2;

    bytes32 private constant AIRDROP_CLAIM_REQUEST = keccak256(bytes("AIRDROP_CLAIM_REQUEST"));

    event AirdropClaimed(
        address indexed receiver,
        uint256[] tokenIds,
        uint256[] amounts,
        uint256[] types,
        address[] collections,
        string internalTxId
    );

    /**
     * @dev Claim NFTs from airdrop event
     * @param data [0] start, [1] end, [2] limitation, [3--] tokenIds, [..] amounts, [..] types, [..] quantities
     * @param addr [0] receiver, [1] signer, [2--] collections
     * @param strs [0] internalTxId
     * @param signatures [0] airdropEventSignature, [1] airdropClaimSignature
     */
    function claimAirdrop(uint256[] memory data, address[] memory addr, string[] memory strs, bytes[] memory signatures)
        public
    {
        // Number of different NFT
        uint256 quantity = addr.length - 2;

        // Current claim amount = sum of amounts[]
        uint256 claimAmount;

        address[] memory collections = new address[](quantity);
        uint256[] memory tokenIds = new uint256[](quantity);
        uint256[] memory amounts = new uint256[](quantity);
        uint256[] memory types = new uint256[](quantity);
        uint256[] memory quantities = new uint256[](quantity);

        /**
         * AirdropClaim
         */
        AirdropClaim memory airdrop = AirdropClaim({
            receiver: addr[0],
            tokenIds: tokenIds,
            amounts: amounts,
            types: types,
            quantities: quantities,
            collections: collections,
            start: data[0],
            end: data[1],
            limitation: data[2],
            nonce: _nonces[AIRDROP_CLAIM_REQUEST][addr[0]],
            airdropEventSignature: signatures[0]
        });

        for (uint256 i = 0; i < quantity; i++) {
            collections[i] = addr[i + 2];
            tokenIds[i] = data[i + 3];
            amounts[i] = data[quantity + i + 3];
            types[i] = data[2 * quantity + i + 3];
            quantities[i] = data[3 * quantity + i + 3];

            // CHECK: Number of NFT to claim must be available
            require(
                claimedAmountPerNFT[airdrop.airdropEventSignature][tokenIds[i]] + amounts[i] <= quantities[i],
                "Airdrop Handler: not enough NFT to claim"
            );

            claimAmount += amounts[i];
        }

        airdrop.amounts = amounts;
        airdrop.tokenIds = tokenIds;
        airdrop.types = types;
        airdrop.collections = collections;
        airdrop.quantities = quantities;

        {
            // CHECK: The airdrop event must not be cancelled before.
            require(!invalidAirdropEvent[airdrop.airdropEventSignature], "Airdrop Handler: Event was cancelled");

            // CHECK: The airdrop event must have been started
            require(block.timestamp >= airdrop.start, "Airdrop Handler: Event has not been started");

            // CHECK: The airdrop event must have not finished
            if (airdrop.end != 0) {
                require(block.timestamp <= airdrop.end, "Airdrop Handler: Event finished");
            }

            if (airdrop.limitation == 0 || airdrop.limitation > 10) {
                // If LIMITLESS or limitation is greater than 10,
                // claimAmount at a time must smaller or equal to 19
                require(claimAmount <= 10, "AirdropHandler: Only claim at most 10 NFTs at a time");
            }

            // CHECK: The signer must be approved
            require(signers[addr[1]], "Airdrop Handler: Only admin's signature");

            if (airdrop.limitation > 0) {
                // CHECK: The amount of the current airdrop claim must be still enough
                // limitation >= claimedAmount + currentClaimAmount
                require(
                    airdrop.limitation
                        >= claimedAmountPerUser[airdrop.airdropEventSignature][airdrop.receiver] + claimAmount,
                    "Airdrop Handler: Exceed the event's limitation"
                );
            }

            // VERIFY: airdropClaimSignature
            require(
                ECDSA.recover(MessageHashUtils.toEthSignedMessageHash(hash(airdrop)), signatures[1]) == addr[1],
                "Airdrop Handler: Fail to verify the airdrop claim"
            );

            for (uint256 i = 0; i < quantity; i++) {
                if (types[i] == ERC_721) {
                    INFTify721(collections[i]).mint(airdrop.receiver, tokenIds[i], "");
                } else if (types[i] == ERC_1155) {
                    INFTify1155(collections[i]).mint(airdrop.receiver, tokenIds[i], amounts[i], "");
                }

                // TODO: Increase the claimed amount of NFT in the event
                claimedAmountPerNFT[airdrop.airdropEventSignature][tokenIds[i]] += amounts[i];
            }

            // TODO: Increase _nonces
            _nonces[AIRDROP_CLAIM_REQUEST][airdrop.receiver]++;

            // TODO: Increase the claimed amount of user in the event
            claimedAmountPerUser[airdrop.airdropEventSignature][airdrop.receiver] += claimAmount;

            emit AirdropClaimed(airdrop.receiver, tokenIds, amounts, types, collections, strs[0]);
        }
    }

    function hash(AirdropClaim memory airdrop) private pure returns (bytes32 digest) {
        return keccak256(
            abi.encodePacked(
                airdrop.receiver,
                airdrop.tokenIds,
                airdrop.amounts,
                airdrop.types,
                airdrop.quantities,
                airdrop.collections,
                airdrop.start,
                airdrop.end,
                airdrop.limitation,
                airdrop.nonce,
                airdrop.airdropEventSignature
            )
        );
    }
}
