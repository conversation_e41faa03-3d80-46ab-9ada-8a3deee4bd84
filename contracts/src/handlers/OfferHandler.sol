// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import {ECDSA} from "@openzeppelin/contracts/utils/cryptography/ECDSA.sol";
import {SafeERC20} from "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import {IERC20} from "@openzeppelin/contracts/token/ERC20/IERC20.sol";

import {Upgradeable} from "../common/Upgradeable.sol";
import {AssemblyUtils} from "../utils/AssemblyUtils.sol";
import {INFTify1155} from "../interfaces/INFTify1155.sol";
import {INFTify721} from "../interfaces/INFTify721.sol";
import {MessageHashUtils} from "@openzeppelin/contracts/utils/cryptography/MessageHashUtils.sol";

// AcceptOffer
struct NftAcceptOffer {
    uint256 tokenID;
    uint256 quantity;
    uint256 sellOrderSupply;
    uint256 enableMakeOffer;
    uint256 sellOrderPrice;
    uint256 offerAmount;
    uint256 offerPrice;
    uint256 listingTime;
    uint256 expirationTime;
    uint256 tokenType; // ERC1155: 0, ERC721: 1
    uint256 partnerType;
    uint256 partnerFee;
    uint256 transactionType;
    address creator;
    address contractAddress;
    address tokenAddress;
    address reciever;
    bytes sellOrderSignature;
    bytes makeOfferSignature;
}

contract NFTOfferHandler is Upgradeable {
    using SafeERC20 for IERC20;

    uint256 constant ERC_721 = 1;
    uint256 constant ERC_1155 = 0;
    address constant ZERO_ADDRESS = address(0);
    uint256 constant NORMAL_TRANSACTION = 0;
    uint256 constant FIAT_PAYMENT = 1;
    uint256 constant GASLESS_TRANSACTION = 2;

    uint256 constant NOT_PARTNER = 0;
    uint256 constant PARTNER = 1;

    event AcceptOfferEvent(
        uint256 _tokenID,
        uint256 _amount,
        uint256 _pricePerItem,
        uint256 _listingTime,
        uint256 _expirationTime,
        address indexed _receiver,
        address indexed _contractAddress,
        uint8 _type, // 0: ProcessedOffer, 1: AcceptedOffer, 2: CanceledOffer
        string internalTxID
    );

    /**
     * @dev Accept offer
     * @param data: [0] tokenID, [1] quantity, [2] sellOrderSupply, [3] enableMakeOffer, [4] sellOrderPrice
     * -------------[5] offerAmount, [6] offerPrice, [7] listingTime, [8] expirationTime, [9] tokenType,
     * -------------[10] partnerType, [11] partnerFee, [12] storeFeeRatio, [13-...] payoutRatio
     * @param addr: [0] creator == artist, [1] contractAddress, [2] tokenAddress, [3] receiver, [4] signer,
     * -------------[5] storeAddress, [6-...] payoutAddress
     * @param strs: [0] internalTxId
     * @param signatures: [0] nftAcceptOfferSignature, [1] sellOrderSignature, [2] makeOfferSignature, [3] payoutSignature
     */
    function acceptOffer(uint256[] memory data, address[] memory addr, string[] memory strs, bytes[] memory signatures)
        public
    {
        /**
         * @dev NftAcceptOffer
         */
        NftAcceptOffer memory nftAcceptOffer = NftAcceptOffer({
            tokenID: data[0],
            quantity: data[1],
            sellOrderSupply: data[2],
            enableMakeOffer: data[3],
            sellOrderPrice: data[4],
            offerAmount: data[5],
            offerPrice: data[6],
            listingTime: data[7],
            expirationTime: data[8],
            tokenType: data[9],
            partnerType: data[10],
            partnerFee: data[11],
            transactionType: 0,
            creator: addr[0],
            contractAddress: addr[1],
            tokenAddress: addr[2],
            reciever: addr[3],
            sellOrderSignature: signatures[1],
            makeOfferSignature: signatures[2]
        });

        {
            // CHECK: the `saleOrder` must be available
            require(!invalidSaleOrder[nftAcceptOffer.sellOrderSignature], "Sell order was cancelled");

            // CHECK: the `makeOffer` must not be canceled
            require(!invalidOffers[nftAcceptOffer.makeOfferSignature], "Offer was cancelled");

            // CHECK: the `makeOffer` must not be accepted
            require(!acceptedOffers[nftAcceptOffer.makeOfferSignature], "Offer was accepted");

            // CHECK: the `saleOrder` must enable make offer
            require(nftAcceptOffer.enableMakeOffer == 1, "NFT's owner does not allow to make offer");

            address signer = addr[4];
            // CHECK: the `signer` must be approved
            require(signers[signer], "Only admin's signature");

            // CHECK:
            require(nftAcceptOffer.offerAmount > 0, "Offer amount must be greater than 0");

            require(msg.sender == nftAcceptOffer.creator, "Should be called by NFT's owner");

            address signerAddress =
                ECDSA.recover(MessageHashUtils.toEthSignedMessageHash(hash(nftAcceptOffer)), signatures[0]);

            require(signerAddress == signer, "NFT's signature is invalid");

            {
                emit AcceptOfferEvent(
                    nftAcceptOffer.tokenID,
                    nftAcceptOffer.offerAmount,
                    nftAcceptOffer.offerPrice,
                    nftAcceptOffer.listingTime,
                    nftAcceptOffer.expirationTime,
                    nftAcceptOffer.reciever,
                    nftAcceptOffer.contractAddress,
                    0,
                    strs[0]
                );
            }

            if (nftAcceptOffer.tokenType == ERC_721) {
                require(
                    soldQuantity[nftAcceptOffer.contractAddress][nftAcceptOffer.tokenID] != 1,
                    "ERC721 token has been sold"
                );
            } else if (nftAcceptOffer.tokenType == ERC_1155) {
                require(
                    nftAcceptOffer.sellOrderSupply - soldQuantityBySaleOrder[nftAcceptOffer.sellOrderSignature]
                        >= nftAcceptOffer.offerAmount,
                    "Not have enough ERC1155 available"
                );
            }

            require(block.timestamp >= nftAcceptOffer.listingTime, "Need to wait until listing time");
            require(
                block.timestamp - nftAcceptOffer.listingTime <= nftAcceptOffer.expirationTime, "Offer has been expired"
            );
        }

        {
            _delegatecall(feeUtils);
        }

        {
            if (nftAcceptOffer.tokenType == ERC_721) {
                INFTify721(nftAcceptOffer.contractAddress).mint(nftAcceptOffer.reciever, nftAcceptOffer.tokenID, "");
            } else if (nftAcceptOffer.tokenType == ERC_1155) {
                INFTify1155(nftAcceptOffer.contractAddress).mint(
                    nftAcceptOffer.reciever, nftAcceptOffer.tokenID, nftAcceptOffer.offerAmount, ""
                );
            }

            soldQuantity[nftAcceptOffer.contractAddress][nftAcceptOffer.tokenID] += nftAcceptOffer.offerAmount;
            soldQuantityBySaleOrder[nftAcceptOffer.sellOrderSignature] += nftAcceptOffer.offerAmount;
            acceptedOffers[nftAcceptOffer.makeOfferSignature] = true;
        }

        {
            emit AcceptOfferEvent(
                nftAcceptOffer.tokenID,
                nftAcceptOffer.offerAmount,
                nftAcceptOffer.offerPrice,
                nftAcceptOffer.listingTime,
                nftAcceptOffer.expirationTime,
                nftAcceptOffer.reciever,
                nftAcceptOffer.contractAddress,
                1,
                strs[0]
            );
        }
    }

    function hash(NftAcceptOffer memory offer) internal pure returns (bytes32 digest) {
        uint256 size = (0x20 * 13) + (0x14 * 4) + offer.sellOrderSignature.length + offer.makeOfferSignature.length;
        bytes memory array = new bytes(size);
        uint256 index;
        assembly {
            index := add(array, 0x20)
        }

        index = AssemblyUtils.writeUint256(index, offer.tokenID);
        index = AssemblyUtils.writeUint256(index, offer.quantity);
        index = AssemblyUtils.writeUint256(index, offer.sellOrderSupply);
        index = AssemblyUtils.writeUint256(index, offer.enableMakeOffer);
        index = AssemblyUtils.writeUint256(index, offer.sellOrderPrice);
        index = AssemblyUtils.writeUint256(index, offer.offerAmount);
        index = AssemblyUtils.writeUint256(index, offer.offerPrice);
        index = AssemblyUtils.writeUint256(index, offer.listingTime);
        index = AssemblyUtils.writeUint256(index, offer.expirationTime);
        index = AssemblyUtils.writeUint256(index, offer.tokenType);
        index = AssemblyUtils.writeUint256(index, offer.partnerType);
        index = AssemblyUtils.writeUint256(index, offer.partnerFee);
        index = AssemblyUtils.writeUint256(index, offer.transactionType);

        index = AssemblyUtils.writeAddress(index, offer.creator);
        index = AssemblyUtils.writeAddress(index, offer.contractAddress);
        index = AssemblyUtils.writeAddress(index, offer.tokenAddress);
        index = AssemblyUtils.writeAddress(index, offer.reciever);

        index = AssemblyUtils.writeBytes(index, offer.sellOrderSignature);
        index = AssemblyUtils.writeBytes(index, offer.makeOfferSignature);

        assembly {
            digest := keccak256(add(array, 0x20), size)
        }
    }
}
