// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import {SafeERC20} from "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import {IERC20} from "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import {ECDSA} from "@openzeppelin/contracts/utils/cryptography/ECDSA.sol";
import {MessageHashUtils} from "@openzeppelin/contracts/utils/cryptography/MessageHashUtils.sol";

import {Upgradeable} from "../common/Upgradeable.sol";
import {AssemblyUtils} from "../utils/AssemblyUtils.sol";
import {INFTify1155} from "../interfaces/INFTify1155.sol";
import {INFTify721} from "../interfaces/INFTify721.sol";

// Resell
struct NftResell {
    uint256 tokenID;
    uint256 royaltyRatio;
    uint256 sellOrderSupply;
    uint256 sellOrderPrice;
    uint256 enableMakeOffer;
    uint256 tokenType; // ERC1155: 0, ERC721: 1
    uint256 partnerType;
    uint256 partnerFee;
    uint256 transactionType;
    address creator;
    address seller;
    address receiver;
    address tokenAddress;
    address contractAddress;
    bytes sellOrderSignature;
}

contract NFTSellHandler is Upgradeable {
    using SafeERC20 for IERC20;

    uint256 constant ERC_721 = 1;
    uint256 constant ERC_1155 = 0;

    event SellNFTEvent(
        uint256 _tokenID,
        uint256 _amount,
        address indexed _seller,
        address indexed _receiver,
        address indexed _collectionAddr,
        uint8 _type, // 0: ProcessingSellRequest, 1: AcceptedSellRequest, 2: CanceledSellRequest
        string internalTxID
    );

    /**
     * @dev Buy from secondary sale
     * @param data: [0] tokenID, [1] royaltyRatio, [2] sellOrderSupply, [3] sellOrderPrice, [4] enableMakeOffer,
     * ------[5] amount, [6] tokenType, [7] partnerType, [8] partnerFee, [9] transactionType,
     * ------[10] storeFeeRatio, [11-...] payoutRatios
     * @param addr: [0] creator == artist, [1] contractAddress, [2] tokenAddress, [3] seller, [4] signer,
     * ------[5] storeAddress, [6] receiver, [7---] payoutAddress
     * @param strs: [0] internalTxId
     * @param signatures: [0] nftResellSignature, [1] sellOrderSignature, [2] payoutSignature
     */
    function sellNowNative(
        uint256[] memory data,
        address[] memory addr,
        string[] memory strs,
        bytes[] memory signatures
    ) public payable {
        NftResell memory nftResell = NftResell({
            tokenID: data[0],
            royaltyRatio: data[1],
            sellOrderSupply: data[2],
            sellOrderPrice: data[3],
            enableMakeOffer: data[4],
            tokenType: data[6],
            partnerType: data[7],
            partnerFee: data[8],
            transactionType: data[9],
            creator: addr[0],
            seller: addr[3],
            receiver: addr[6],
            tokenAddress: addr[2],
            contractAddress: addr[1],
            sellOrderSignature: signatures[1]
        });

        uint256 amount = data[5];

        {
            emit SellNFTEvent(
                nftResell.tokenID,
                amount,
                nftResell.seller,
                nftResell.receiver,
                nftResell.contractAddress,
                0,
                strs[0]
            );
        }

        {
            // CHECK: the `saleOrder` was canceled or not
            require(
                !invalidSaleOrder[nftResell.sellOrderSignature],
                "Sell order was cancelled"
            );

            // CHECK:
            require(amount > 0, "Buying amount must be greater than 0");
        }
        {
            address signer = addr[4];

            // CHECK: the `signer` must be approved
            require(signers[signer], "Only admin's signer");

            // CHECK:
            require(
                ECDSA.recover(
                    MessageHashUtils.toEthSignedMessageHash(hash(nftResell)),
                    signatures[0]
                ) == signer,
                "NFT is invalid"
            );

            if (nftResell.tokenType == ERC_721) {
                require(
                    soldQuantityBySaleOrder[nftResell.sellOrderSignature] != 1,
                    "ERC721 token has been sold"
                );
            } else if (nftResell.tokenType == ERC_1155) {
                require(
                    nftResell.sellOrderSupply -
                        soldQuantityBySaleOrder[nftResell.sellOrderSignature] >=
                        amount,
                    "Not have enough ERC1155 available"
                );
            }
        }
        {
            if (nftResell.sellOrderPrice > 0) {
                _delegatecall(feeUtils);
            }
        }

        {
            if (nftResell.tokenType == ERC_1155) {
                INFTify1155(nftResell.contractAddress).safeTransferFrom(
                    nftResell.seller,
                    nftResell.receiver,
                    nftResell.tokenID,
                    amount,
                    ""
                );
            } else if (nftResell.tokenType == ERC_721) {
                INFTify721(nftResell.contractAddress).safeTransferFrom(
                    nftResell.seller,
                    nftResell.receiver,
                    nftResell.tokenID,
                    ""
                );
            }

            soldQuantityBySaleOrder[nftResell.sellOrderSignature] += amount;
        }

        {
            emit SellNFTEvent(
                nftResell.tokenID,
                amount,
                nftResell.seller,
                nftResell.receiver,
                nftResell.contractAddress,
                1,
                strs[0]
            );
        }
    }

    function hash(NftResell memory resell)
        internal
        pure
        returns (bytes32 digest)
    {
        uint256 size = (0x20 * 9) +
            (0x14 * 5) +
            resell.sellOrderSignature.length;
        bytes memory array = new bytes(size);
        uint256 index;
        assembly {
            index := add(array, 0x20)
        }

        index = AssemblyUtils.writeUint256(index, resell.tokenID);
        index = AssemblyUtils.writeUint256(index, resell.royaltyRatio);
        index = AssemblyUtils.writeUint256(index, resell.sellOrderSupply);
        index = AssemblyUtils.writeUint256(index, resell.sellOrderPrice);
        index = AssemblyUtils.writeUint256(index, resell.enableMakeOffer);
        index = AssemblyUtils.writeUint256(index, resell.tokenType);
        index = AssemblyUtils.writeUint256(index, resell.partnerType);
        index = AssemblyUtils.writeUint256(index, resell.partnerFee);
        index = AssemblyUtils.writeUint256(index, resell.transactionType);

        index = AssemblyUtils.writeAddress(index, resell.creator);
        index = AssemblyUtils.writeAddress(index, resell.seller);
        index = AssemblyUtils.writeAddress(index, resell.receiver);
        index = AssemblyUtils.writeAddress(index, resell.tokenAddress);
        index = AssemblyUtils.writeAddress(index, resell.contractAddress);

        index = AssemblyUtils.writeBytes(index, resell.sellOrderSignature);

        assembly {
            digest := keccak256(add(array, 0x20), size)
        }
    }
}
