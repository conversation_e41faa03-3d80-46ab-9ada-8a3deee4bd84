// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import {Upgradeable} from "../common/Upgradeable.sol";
import {SignatureChecker} from "@openzeppelin/contracts/utils/cryptography/SignatureChecker.sol";
import {ECDSA} from "@openzeppelin/contracts/utils/cryptography/ECDSA.sol";
import {MessageHashUtils} from "@openzeppelin/contracts/utils/cryptography/MessageHashUtils.sol";


contract NFTMetaHandler is Upgradeable {
    address immutable proxy;
    /**
     * This are save as immutable for cheap access
     * The chainId is also saved to be able to recompute domainSeparator in the case of fork
     */
    bytes32 private immutable _domainSeparator;
    uint256 private immutable _domainChainId;

    string public constant BUY_FROM_PRIMARY_SALE = "Buy from primary sale";
    string public constant BUY_FROM_SECONDARY_SALE = "Buy from secondary sale";
    string public constant CANCEL_SALE_ORDER = "Cancel sale order";
    string public constant CANCEL_OFFER = "Cancel offer";
    string public constant TRANSFER_NFT = "Transfer NFT";
    string public constant OPEN_BOX = "Open mystery box";
    string public constant AIRDROP_CLAIM = "Claim airdrop NFT";

    // * REQUEST
    bytes32 public constant PRIMARY_SALE_REQUEST =
        keccak256(bytes("PRIMARY_SALE_REQUEST"));
    bytes32 public constant SECONDARY_SALE_REQUEST =
        keccak256(bytes("SECONDARY_SALE_REQUEST"));
    bytes32 public constant CANCEL_SALE_ORDER_REQUEST =
        keccak256(bytes("CANCEL_SALE_ORDER_REQUEST"));
    bytes32 public constant CANCEL_OFFER_REQUEST =
        keccak256(bytes("CANCEL_OFFER_REQUEST"));
    bytes32 public constant TRANSFER_NFT_REQUEST =
        keccak256(bytes("TRANSFER_NFT_REQUEST"));
    bytes32 public constant BOX_OPEN_REQUEST =
        keccak256(bytes("BOX_OPEN_REQUEST"));
    bytes32 public constant AIRDROP_CLAIM_REQUEST =
        keccak256(bytes("AIRDROP_CLAIM_REQUEST"));


    string public constant name = "NFTify";
    string public constant version = "1";

    constructor(address proxy_) {
        proxy = proxy_;

        uint256 chainId;

        //solhint-disable-next-line no-inline-assembly
        assembly {
            chainId := chainid()
        }

        _domainChainId = chainId;
        _domainSeparator = _calculateDomainSeparator(chainId, proxy_);
    }

    /**
     * @notice Builds the DOMAIN_SEPARATOR (eip712) at time of use
     * @dev This is not set as a constant, to ensure that the chainId will change in the event of a chain fork
     * @return the DOMAIN_SEPARATOR of eip712
     */
    function DOMAIN_SEPARATOR() public view returns (bytes32) {
        uint256 chainId;

        //solhint-disable-next-line no-inline-assembly
        assembly {
            chainId := chainid()
        }

        return
            (chainId == _domainChainId)
                ? _domainSeparator
                : _calculateDomainSeparator(chainId, proxy);
    }

    function _calculateDomainSeparator(
        uint256 chainId,
        address verifyingContract
    ) internal pure returns (bytes32) {
        return
            keccak256(
                abi.encode(
                    keccak256(
                        "EIP712Domain(string name,string version,uint256 chainId,address verifyingContract)"
                    ),
                    keccak256(bytes("NFTify")),
                    keccak256(bytes("1")),
                    chainId,
                    address(verifyingContract)
                )
            );
    }

    /**
     * @dev Execute meta transaction

     * ? -- BOX_OPEN_REQUEST --
     * -- data [0] tokenType, [1] tokenID, [2] quantity
     * -- addrs [0] from, [1] targetContract, [2] collection
     * 
     * ? -- AIRDROP_CLAIM_REQUEST --
     * -- data [0] start, [1] end, [2] limitation, [3] quantity
     * -- addrs [0] receiver
     * ? -- OTHERS --
     * -- data [0] tokenType, [1] tokenID, [2] quantity, [3] price
     * -- addrs [0] from, [1] targetContract, [2] collection, [3] seller, [4] paymentToken
     */

    function executeMetaTransaction(
        uint256[] memory data,
        address[] memory addrs,
        bytes[] memory signatures,
        bytes32 requestType,
        uint8 v,
        bytes32 r,
        bytes32 s
    ) public {
        MetaTransaction memory metaTx = MetaTransaction({
            nonce: nonces[addrs[0]],
            from: addrs[0],
            targetContract: proxy,
            request: ""
        });

        Item memory item;

        if (
            requestType != AIRDROP_CLAIM_REQUEST &&
            requestType != BOX_OPEN_REQUEST
        ) {
            item = Item({
                tokenID: data[1],
                tokenType: data[0],
                collection: addrs[2]
            });
        }

        bytes32 typedDataHash;

        if (
            requestType == PRIMARY_SALE_REQUEST ||
            requestType == SECONDARY_SALE_REQUEST
        ) {
            metaTx.request = requestType == PRIMARY_SALE_REQUEST
                ? BUY_FROM_PRIMARY_SALE
                : BUY_FROM_SECONDARY_SALE;

            BuyRequest memory buyRequest = BuyRequest({
                metaTx: metaTx,
                item: item,
                quantity: data[2],
                price: data[3],
                seller: addrs[3],
                paymentToken: addrs[4]
            });

            // * Get the hash
            typedDataHash = hash(buyRequest);
        } else if (requestType == CANCEL_SALE_ORDER_REQUEST) {
            metaTx.request = CANCEL_SALE_ORDER;

            CancelSaleOrderRequest
                memory cancelRequest = CancelSaleOrderRequest({
                    metaTx: metaTx,
                    item: item,
                    quantity: data[2],
                    price: data[3]
                });

            // * Get the hash
            typedDataHash = hash(cancelRequest);
        } else if (requestType == CANCEL_OFFER_REQUEST) {
            metaTx.request = CANCEL_OFFER;

            CancelOfferRequest memory cancelRequest = CancelOfferRequest({
                metaTx: metaTx,
                item: item,
                quantity: data[2],
                yourOffer: data[3]
            });

            // * Get the hash
            typedDataHash = hash(cancelRequest);
        } else if (requestType == TRANSFER_NFT_REQUEST) {
            metaTx.request = TRANSFER_NFT;

            TransferNFTRequest memory request = TransferNFTRequest({
                metaTx: metaTx,
                item: item,
                quantity: data[2],
                from: addrs[3],
                to: addrs[4]
            });

            typedDataHash = hash(request);
        } else if (requestType == BOX_OPEN_REQUEST) {
            metaTx.request = OPEN_BOX;

            BoxOpenRequest memory request = BoxOpenRequest({
                metaTx: metaTx,
                boxId: data[1],
                boxAddress: addrs[2],
                quantity: data[2]
            });

            typedDataHash = hash(request);
        } else if (requestType == AIRDROP_CLAIM_REQUEST) {
            metaTx.request = AIRDROP_CLAIM;

            AirdropClaimRequest memory request = AirdropClaimRequest({
                metaTx: metaTx,
                start: data[0],
                end: data[1],
                limitation: data[2],
                quantity: data[3],
                receiver: addrs[0]
            });

            typedDataHash = hash(request);
        }

        bytes32 digest = MessageHashUtils.toTypedDataHash(
            DOMAIN_SEPARATOR(),
            typedDataHash
        );

        (address recoveredAddress,, ) = ECDSA.tryRecover(digest, v, r, s);

        require(
            recoveredAddress == metaTx.from,
            "MetaTransaction: invalid signature"
        );

        nonces[metaTx.from]++;

        // Currently, the contract is locked by calling `executeMetaTransaction`.
        // So, to call another function, we have to unlock it.
        // That's why have change the state of `reentrancyLock` here.
        reentrancyLock = 0;
        (bool success, ) = metaTx.targetContract.call(
            abi.encodePacked(signatures[0], metaTx.from)
        );

        require(success, "NFTifyMetaTx: function call not success");
    }

    function hash(MetaTransaction memory metaTx)
        internal
        pure
        returns (bytes32)
    {
        return
            keccak256(
                abi.encode(
                    keccak256(
                        bytes(
                            "MetaTransaction(uint256 nonce,address from,address targetContract,string request)"
                        )
                    ),
                    metaTx.nonce,
                    metaTx.from,
                    metaTx.targetContract,
                    keccak256(bytes(metaTx.request))
                )
            );
    }

    function hash(Item memory item) internal pure returns (bytes32) {
        return
            keccak256(
                abi.encode(
                    keccak256(
                        bytes(
                            "Item(uint256 tokenID,uint256 tokenType,address collection)"
                        )
                    ),
                    item.tokenID,
                    item.tokenType,
                    item.collection
                )
            );
    }

    function hash(BuyRequest memory buyRequest)
        internal
        pure
        returns (bytes32)
    {
        return
            keccak256(
                abi.encode(
                    keccak256(
                        bytes(
                            "BuyRequest(MetaTransaction metaTx,Item item,uint256 quantity,uint256 price,address seller,address paymentToken)Item(uint256 tokenID,uint256 tokenType,address collection)MetaTransaction(uint256 nonce,address from,address targetContract,string request)"
                        )
                    ),
                    hash(buyRequest.metaTx),
                    hash(buyRequest.item),
                    buyRequest.quantity,
                    buyRequest.price,
                    buyRequest.seller,
                    buyRequest.paymentToken
                )
            );
    }

    function hash(CancelSaleOrderRequest memory cancelRequest)
        internal
        pure
        returns (bytes32)
    {
        return (
            keccak256(
                abi.encode(
                    keccak256(
                        bytes(
                            "CancelSaleOrderRequest(MetaTransaction metaTx,Item item,uint256 quantity,uint256 price)Item(uint256 tokenID,uint256 tokenType,address collection)MetaTransaction(uint256 nonce,address from,address targetContract,string request)"
                        )
                    ),
                    hash(cancelRequest.metaTx),
                    hash(cancelRequest.item),
                    cancelRequest.quantity,
                    cancelRequest.price
                )
            )
        );
    }

    function hash(CancelOfferRequest memory cancelRequest)
        internal
        pure
        returns (bytes32)
    {
        return (
            keccak256(
                abi.encode(
                    keccak256(
                        bytes(
                            "CancelOfferRequest(MetaTransaction metaTx,Item item,uint256 quantity,uint256 yourOffer)Item(uint256 tokenID,uint256 tokenType,address collection)MetaTransaction(uint256 nonce,address from,address targetContract,string request)"
                        )
                    ),
                    hash(cancelRequest.metaTx),
                    hash(cancelRequest.item),
                    cancelRequest.quantity,
                    cancelRequest.yourOffer
                )
            )
        );
    }

    function hash(TransferNFTRequest memory transferRequest)
        internal
        pure
        returns (bytes32)
    {
        return
            keccak256(
                abi.encode(
                    keccak256(
                        bytes(
                            "TransferNFTRequest(MetaTransaction metaTx,Item item,uint256 quantity,address from,address to)Item(uint256 tokenID,uint256 tokenType,address collection)MetaTransaction(uint256 nonce,address from,address targetContract,string request)"
                        )
                    ),
                    hash(transferRequest.metaTx),
                    hash(transferRequest.item),
                    transferRequest.quantity,
                    transferRequest.from,
                    transferRequest.to
                )
            );
    }

    function hash(BoxOpenRequest memory boxOpenRequest)
        internal
        pure
        returns (bytes32)
    {
        return
            keccak256(
                abi.encode(
                    keccak256(
                        bytes(
                            "BoxOpenRequest(MetaTransaction metaTx,uint256 boxId,address boxAddress,uint256 quantity)MetaTransaction(uint256 nonce,address from,address targetContract,string request)"
                        )
                    ),
                    hash(boxOpenRequest.metaTx),
                    boxOpenRequest.boxId,
                    boxOpenRequest.boxAddress,
                    boxOpenRequest.quantity
                )
            );
    }

    function hash(AirdropClaimRequest memory airdropClaimRequest)
        internal
        pure
        returns (bytes32)
    {
        return
            keccak256(
                abi.encode(
                    keccak256(
                        bytes(
                            "AirdropClaimRequest(MetaTransaction metaTx,uint256 start,uint256 end,uint256 limitation,uint256 quantity,address receiver)MetaTransaction(uint256 nonce,address from,address targetContract,string request)"
                        )
                    ),
                    hash(airdropClaimRequest.metaTx),
                    airdropClaimRequest.start,
                    airdropClaimRequest.end,
                    airdropClaimRequest.limitation,
                    airdropClaimRequest.quantity,
                    airdropClaimRequest.receiver
                )
            );
    }
}

// * EIP712Domain
struct EIP712Domain {
    string name;
    string version;
    uint256 chainId;
    address verifyingContract;
}

// * Item
struct Item {
    uint256 tokenID;
    uint256 tokenType;
    address collection;
}

// * MetaTransaction
struct MetaTransaction {
    uint256 nonce;
    address from;
    address targetContract;
    string request;
}

// * BuyRequest
struct BuyRequest {
    MetaTransaction metaTx;
    Item item;
    uint256 quantity;
    uint256 price;
    address seller;
    address paymentToken;
}

// * CancelSaleOrderRequest
struct CancelSaleOrderRequest {
    MetaTransaction metaTx;
    Item item;
    uint256 quantity;
    uint256 price;
}

// * CancelOfferRequest
struct CancelOfferRequest {
    MetaTransaction metaTx;
    Item item;
    uint256 quantity;
    uint256 yourOffer;
}

// * TransferNFTRequest
struct TransferNFTRequest {
    MetaTransaction metaTx;
    Item item;
    uint256 quantity;
    address from;
    address to;
}

// * BoxOpenRequest
struct BoxOpenRequest {
    MetaTransaction metaTx;
    uint256 boxId;
    address boxAddress;
    uint256 quantity;
}

// * AirdropClaim
struct AirdropClaimRequest {
    MetaTransaction metaTx;
    uint256 start;
    uint256 end;
    uint256 limitation;
    uint256 quantity;
    address receiver;
}
