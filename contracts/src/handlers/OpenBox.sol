// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import {ECDSA} from "@openzeppelin/contracts/utils/cryptography/ECDSA.sol";
import {MessageHashUtils} from "@openzeppelin/contracts/utils/cryptography/MessageHashUtils.sol";

import {Upgradeable} from "../common/Upgradeable.sol";
import {AssemblyUtils} from "../utils/AssemblyUtils.sol";
import {INFTify1155} from "../interfaces/INFTify1155.sol";
import {INFTify721} from "../interfaces/INFTify721.sol";

struct Box {
    uint256 boxID;
    uint256 tokenID;
    address owner;
    address boxCollection;
    address tokenCollection;
}

struct BoxOpen {
    bytes[] boxSignatures;
}

contract NFTOpenBox is Upgradeable {
    uint256 constant ERC_721 = 1;
    uint256 constant ERC_1155 = 0;

    event BoxOpened(
        address indexed owner,
        uint256 boxId,
        address indexed boxCollection,
        uint256[] tokenIds,
        address[] collections,
        string internalTxId
    );

    /**
     * @dev Open box
     * @param data [0] box id, [1-...] token ids
     * @param addr [0] owner, [1] signer, [2] box's collection, [3-...] token's collection
     * strs [0] internalTxId
     * @param signatures [0] openBoxSignature, [1-...] boxSignatures
     */
    function openBox(uint256[] memory data, address[] memory addr, string[] memory, bytes[] memory signatures) public {
        uint256[] memory tokenIds = new uint256[](data.length - 1);
        address[] memory tokenCollections = new address[](tokenIds.length);
        bytes[] memory boxSignatures = new bytes[](tokenIds.length);

        for (uint256 i = 0; i < tokenIds.length; i++) {
            tokenIds[i] = data[i + 1];
            tokenCollections[i] = addr[i + 3];
            boxSignatures[i] = signatures[i + 1];
        }

        Box memory box =
            Box({boxID: data[0], tokenID: 0, owner: addr[0], boxCollection: addr[2], tokenCollection: address(0)});

        BoxOpen memory boxOpen = BoxOpen({boxSignatures: boxSignatures});

        {
            require(!openedBoxSignatures[signatures[0]], "cannot use this signature");

            require(signers[addr[1]], "only admin signature");

            for (uint256 i = 0; i < tokenIds.length; i++) {
                box.tokenID = tokenIds[i];
                box.tokenCollection = tokenCollections[i];

                address actualSigner =
                    ECDSA.recover(MessageHashUtils.toEthSignedMessageHash(hash(box)), boxSignatures[i]);
                require(actualSigner == addr[1], "Box is invalid");
            }

            address signerAddress =
                ECDSA.recover(MessageHashUtils.toEthSignedMessageHash(hash(boxOpen)), signatures[0]);

            require(signerAddress == addr[1], "Box open is invalid");
        }

        {
            // Burn the box
            INFTify1155(box.boxCollection).burn(box.owner, box.boxID, tokenIds.length);

            // Mint the tokens
            for (uint256 i = 0; i < tokenIds.length; i++) {
                INFTify721(tokenCollections[i]).mint(box.owner, tokenIds[i], "");
            }

            openedBoxSignatures[signatures[0]] = true;
        }

        emit BoxOpened(box.owner, box.boxID, box.boxCollection, tokenIds, tokenCollections, "");
    }

    function hash(Box memory box) internal pure returns (bytes32 digest) {
        digest = keccak256(abi.encodePacked(box.boxID, box.tokenID, box.owner, box.boxCollection, box.tokenCollection));
    }

    function hash(BoxOpen memory boxOpen) internal pure returns (bytes32 digest) {
        uint256 size = boxOpen.boxSignatures.length * boxOpen.boxSignatures[0].length;
        bytes memory array = new bytes(size);
        uint256 index;

        assembly {
            index := add(array, 0x20)
        }

        for (uint256 i = 0; i < boxOpen.boxSignatures.length; i++) {
            index = AssemblyUtils.writeBytes(index, boxOpen.boxSignatures[i]);
        }

        assembly {
            digest := keccak256(add(array, 0x20), size)
        }
    }
}
