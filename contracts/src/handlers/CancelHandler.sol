// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import {ECDSA} from "@openzeppelin/contracts/utils/cryptography/ECDSA.sol";
import {Upgradeable} from "../common/Upgradeable.sol";
import {AssemblyUtils} from "../utils/AssemblyUtils.sol";
import {MessageHashUtils} from "@openzeppelin/contracts/utils/cryptography/MessageHashUtils.sol";


// SellOrder
struct NftSellOrder {
    uint256 sellOrderID; // 0x+mongoDBID
    uint256 sellOrderSupply;
    address creator;
}

// MakeOffer
struct NftMakeOffer {
    uint256 makeOfferID; // 0x+mongoDBID
    address creator;
}

// CancelBatch
struct CancelBatch {
    bytes[] signatures;
}

// BurnRedeemEvent
struct BurnRedeemEvent {
    bytes eventId; 
    uint256 totalRedeemTimes;
    address creator;
    uint256 start;
    uint256 end;
}

// AirdropEvent
struct AirdropEvent {
    address creator;
    uint256 eventId;
    uint256 quantity;
    uint256 start;
    uint256 end;
}

contract NFTCancelHandler is Upgradeable {
    using AssemblyUtils for uint256;

    event CancelEvent(
        address indexed _creator,
        uint256 _type, //  1:CancelOffer, 2: CancelSellOrder | CancelBurnRedeem, 3: CancelSellOrderResell
        string internalTxID
    );

    event CancelBatchEvent(
        address indexed caller,
        uint256 type_,
        string[] internalTxIds
    );

    /**
     * @dev Cancel burn redeem event
     * @param data [0] totalRedeemTimes [1] start, [2] end
     * @param addr [0] creator
     * @param []
     * @param signatures [0] cancelBurnRedeemSignature [1] eventId
     */
    function cancelBurnRedeemEvent(
        uint256[] memory data,
        address[] memory addr,
        string[] memory,
        bytes[] memory signatures
    ) public {
        require(
            data[1] <= block.timestamp || data[1] == 0,
            "BurnRedeemHandler: burn & redeem event haven't started"
        );
        require(
            data[2] >= block.timestamp || data[2] == 0,
            "BurnRedeemHandler: burn & redeem event was ended"
        );
        require(
            !invalidBurnRedeemEvents[signatures[1]],
            "BurnRedeemHandler: event was cancelled"
        );
        require(
            burnRedeemTimes[signatures[1]] != data[0],
            "CancelHandler: already redeemed all nfts in event"
        );

        BurnRedeemEvent memory item = BurnRedeemEvent(
            signatures[1],
            data[0],
            addr[0],
            data[1],
            data[2]
        );

        bytes32 digest = MessageHashUtils.toEthSignedMessageHash(hash(item));
        address recoveredAddr = ECDSA.recover(digest, signatures[0]);

        require(
            signers[recoveredAddr],
            "CancelHandler: Only signer's signature"
        );

        invalidBurnRedeemEvents[signatures[1]] = true;

        emit CancelEvent(addr[0], 2, string(signatures[1]));
    }

    /**
     * @dev Handle cancel order
     * @param data (0) saleOrderID, (1) saleOrderSupply, (2) type
     * @param addr (0) signer, (1) creator
     * @param strs [...] internalTxId
     * @param signatures [0...] saleOrderSignatures, cancelBatchSignature
     */
    function cancelSaleOrder(
        uint256[] memory data,
        address[] memory addr,
        string[] memory strs,
        bytes[] memory signatures
    ) public {
        require(signers[addr[0]], "Only admin's signature");

        if (strs.length == 1) {
            require(
                soldQuantityBySaleOrder[signatures[0]] != data[1],
                "Sell order has been sold out"
            );

            require(
                !invalidSaleOrder[signatures[0]],
                "Sell order was canceled"
            );

            NftSellOrder memory item = NftSellOrder(data[0], data[1], addr[1]);

            require(
                ECDSA.recover(
                    MessageHashUtils.toEthSignedMessageHash(hash(item)),
                    signatures[0]
                ) == addr[0],
                "Sell order signature is invalid"
            );

            invalidSaleOrder[signatures[0]] = true;

            emit CancelEvent(addr[1], data[2], strs[0]);
        } else {
            bytes[] memory cancelSignature = new bytes[](signatures.length - 1);

            for (uint256 i = 0; i < cancelSignature.length; i++) {
                require(
                    !invalidSaleOrder[signatures[i]],
                    "Sale order was cancelled"
                );
                cancelSignature[i] = signatures[i];
            }

            CancelBatch memory item = CancelBatch({
                signatures: cancelSignature
            });
            require(
                ECDSA.recover(
                    MessageHashUtils.toEthSignedMessageHash(hash(item)),
                    signatures[signatures.length - 1]
                ) == addr[0],
                "Fail to cancel bulk sale order"
            );

            for (uint256 i = 0; i < cancelSignature.length; i++) {
                invalidSaleOrder[cancelSignature[i]] = true;
            }

            emit CancelBatchEvent(msg.sender, data[2], strs);
        }
    }

    /**
     * @param data (0) makeOfferID, (1) type
     * @param addr (0) signer, (1) offer maker
     * @param strs (0) internalTxID
     * @param signatures (0) makeOfferSignature
     */
    function cancelOffer(
        uint256[] memory data,
        address[] memory addr,
        string[] memory strs,
        bytes[] memory signatures
    ) public {
        require(!acceptedOffers[signatures[0]], "Offer was accepted");

        require(!invalidOffers[signatures[0]], "Offer was cancelled");

        require(signers[addr[0]], "Only signers");

        NftMakeOffer memory item = NftMakeOffer(data[0], addr[1]);

        require(
            ECDSA.recover(
                MessageHashUtils.toEthSignedMessageHash(hash(item)),
                signatures[0]
            ) == addr[0],
            "Offer signature is invalid"
        );

        invalidOffers[signatures[0]] = true;

        emit CancelEvent(addr[1], data[1], strs[0]);
    }

    /**
     * @dev Cancel airdrop event
     * @param data [0] start, [1] end, [2] quantity, [3] eventId, [4] type
     * @param addr [0] creator, [1] signer
     * @param strs [0] internalTxId
     * @param signatures [0] airdropEventSignature
     */
    function cancelAirdropEvent(
        uint256[] memory data,
        address[] memory addr,
        string[] memory strs,
        bytes[] memory signatures
    ) public {
        AirdropEvent memory airdrop = AirdropEvent({
            creator: addr[0],
            eventId: data[3],
            quantity: data[2],
            start: data[0],
            end: data[1]
        });

        // CHECK: The event must not be cancelled before
        require(
            !invalidAirdropEvent[signatures[0]],
            "Cancel Handler: airdrop event was cancelled"
        );

        // CHECK: The event are happenning

        require(
            block.timestamp >= airdrop.start,
            "CancelHandler: event has not been started"
        );

        if (airdrop.end != 0) {
            require(
                block.timestamp <= airdrop.end,
                "CancelHandler: event ended"
            );
        }

        // CHECK: The msg.sender must be the event's creator
        require(
            msg.sender == airdrop.creator,
            "CancelHandler: only event's creator"
        );

        // CHECK: The signer must be approved
        require(signers[addr[1]], "CancelHandler: only admin's signature");

        // VERIFY: airdropEventSignature
        require(
            ECDSA.recover(
                MessageHashUtils.toEthSignedMessageHash(hash(airdrop)),
                signatures[0]
            ) == addr[1],
            "CancelHandler: event is not valid"
        );

        // TODO: invalid the airdrop event
        invalidAirdropEvent[signatures[0]] = true;

        emit CancelEvent(airdrop.creator, data[4], strs[0]);
    }

    function hash(
        NftSellOrder memory sellOrder
    ) internal pure returns (bytes32 digest) {
        digest = keccak256(
            abi.encodePacked(
                sellOrder.sellOrderID,
                sellOrder.sellOrderSupply,
                sellOrder.creator
            )
        );
    }

    function hash(
        NftMakeOffer memory makeOffer
    ) internal pure returns (bytes32 digest) {
        digest = keccak256(
            abi.encodePacked(makeOffer.makeOfferID, makeOffer.creator)
        );
    }

    function hash(
        CancelBatch memory cancelBatch
    ) internal pure returns (bytes32 digest) {
        uint256 size = cancelBatch.signatures.length *
            cancelBatch.signatures[0].length;
        bytes memory array = new bytes(size);
        uint256 index;

        assembly {
            index := add(array, 0x20)
        }

        for (uint256 i = 0; i < cancelBatch.signatures.length; i++) {
            index = AssemblyUtils.writeBytes(index, cancelBatch.signatures[i]);
        }

        assembly {
            digest := keccak256(add(array, 0x20), size)
        }
    }

    function hash(
        AirdropEvent memory airdrop
    ) private pure returns (bytes32 digest) {
        return
            keccak256(
                abi.encodePacked(
                    airdrop.creator,
                    airdrop.eventId,
                    airdrop.quantity,
                    airdrop.start,
                    airdrop.end
                )
            );
    }

    function hash(
        BurnRedeemEvent memory burnRedeem
    ) private pure returns (bytes32 digest) {
        return
            keccak256(
                abi.encodePacked(
                    burnRedeem.eventId,
                    burnRedeem.totalRedeemTimes,
                    burnRedeem.creator,
                    burnRedeem.start,
                    burnRedeem.end
                )
            );
    }
}
