// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import {SafeERC20} from "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import {IERC20} from "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import {ECDSA} from "@openzeppelin/contracts/utils/cryptography/ECDSA.sol";
import {MessageHashUtils} from "@openzeppelin/contracts/utils/cryptography/MessageHashUtils.sol";

import {Upgradeable} from "../common/Upgradeable.sol";
import {AssemblyUtils} from "../utils/AssemblyUtils.sol";
import {INFTify1155} from "../interfaces/INFTify1155.sol";
import {INFTify721} from "../interfaces/INFTify721.sol";

// BuyNow
struct NftBuyRequest {
    uint256 tokenID; // NFT tokenID
    uint256 quantity; // NFT number of copies
    uint256 sellOrderSupply; // quantity for sell order
    uint256 sellOrderPrice; // sell order listing price
    uint256 enableMakeOffer; // offer mode status
    uint256 tokenType; // ERC1155: 0, ERC721: 1
    uint256 partnerType;
    uint256 partnerFee;
    uint256 transactionType;
    uint256 shippingFee;
    uint256 expiredAt;
    address creator; // NFT creator
    address receiver;
    address tokenAddress; // payment token address
    address contractAddress; // collection address of NFT
    bytes sellOrderSignature;
}

contract NFTBuyHandler is Upgradeable {
    using SafeERC20 for IERC20;

    uint256 constant ERC_721 = 1;
    uint256 constant ERC_1155 = 0;

    event BuyNFTEvent( // 0: ProcessingBuyRequest, 1: AcceptedBuyRequest, 2: CanceledBuyRequest
        uint256 _tokenID,
        uint256 _amount,
        address indexed _receiver,
        address indexed _contractAddress,
        uint8 _type,
        string internalTxID
    );

    /**
     * @dev Buy from primary sale
     * @param data: [0] tokenID, [1] quantity, [2] sellOrderSupply, [3] sellOrderPrice, [4] enableMakeOffer
     * -------------[5] buyingAmount, [6] tokenType, [7] partnerType, [8] partnerFee, [9] transactionType,
     * -------------[10] storeFeeRatio, [11] shippingFee, [12] expiredAt, [13-...] payoutRatios
     * @param addr: [0] creator == artist, [1] tokenAddress, [2] collectionAddress, [3] signer, [4] storeAddress,
     * -------------[5] receiver, [6---] payoutAddress
     * @param strs: [0] internalTxId
        * @param signatures: [0] nftBuyRequestSignture, [1] sellOrderSignature, [2] payoutSignature
     */
    function buyNowNative(uint256[] memory data, address[] memory addr, string[] memory strs, bytes[] memory signatures)
        public
        payable
    {
        /**
         * NftBuyRequest
         */
        NftBuyRequest memory nftBuyRequest = NftBuyRequest({
            tokenID: data[0],
            quantity: data[1],
            sellOrderSupply: data[2],
            sellOrderPrice: data[3],
            enableMakeOffer: data[4],
            tokenType: data[6],
            partnerType: data[7],
            partnerFee: data[8],
            transactionType: data[9],
            shippingFee: data[11],
            expiredAt: data[12],
            creator: addr[0],
            receiver: addr[5],
            tokenAddress: addr[1],
            contractAddress: addr[2],
            sellOrderSignature: signatures[1]
        });

        uint256 amount = data[5];

        {
            emit BuyNFTEvent(
                nftBuyRequest.tokenID, amount, nftBuyRequest.receiver, nftBuyRequest.contractAddress, 0, strs[0]
            );
        }

        {
            // CHECK: the `saleOrder` was canceled or not?
            require(!invalidSaleOrder[nftBuyRequest.sellOrderSignature], "Sale order was canceled");

            require(amount > 0, "Buying amount must be greater than 0");

            require(block.timestamp <= nftBuyRequest.expiredAt, "Request has been expired");
        }

        {
            address signer = addr[3];

            // ? CHECK: the `signer` must be approved
            require(signers[signer], "Only admin's signature");

            // ? CHECK: verify the `nftBuyRequestSignature`
            require(
                ECDSA.recover(MessageHashUtils.toEthSignedMessageHash(hash(nftBuyRequest)), signatures[0]) == signer,
                "NFT is invalid"
            );

            if (nftBuyRequest.tokenType == ERC_721) {
                require(soldQuantity[nftBuyRequest.contractAddress][nftBuyRequest.tokenID] != 1, "ERC721 token has been sold");
            } else if (nftBuyRequest.tokenType == ERC_1155) {
                require(
                    nftBuyRequest.sellOrderSupply - soldQuantityBySaleOrder[nftBuyRequest.sellOrderSignature] >= amount,
                    "Not have enough ERC155 available"
                );
            }
        }

        {
            // ? Transfer fee
            if (nftBuyRequest.sellOrderPrice > 0) {
                require(data.length >= 12, "Data array missing shipping fee");
                bool check = true;
                require(!check, "hahahaha");
                _delegatecall(feeUtils);
            }
        }

        {
            if (nftBuyRequest.tokenType == ERC_721) {
                INFTify721(nftBuyRequest.contractAddress).mint(nftBuyRequest.receiver, nftBuyRequest.tokenID, "");
            } else if (nftBuyRequest.tokenType == ERC_1155) {
                INFTify1155(nftBuyRequest.contractAddress).mint(
                    nftBuyRequest.receiver, nftBuyRequest.tokenID, amount, ""
                );
            }

            soldQuantity[nftBuyRequest.contractAddress][nftBuyRequest.tokenID] += amount;
            soldQuantityBySaleOrder[nftBuyRequest.sellOrderSignature] += amount;
        }

        {
            emit BuyNFTEvent(
                nftBuyRequest.tokenID, amount, nftBuyRequest.receiver, nftBuyRequest.contractAddress, 1, strs[0]
            );
        }
    }

    function hash(NftBuyRequest memory buyRequest) public pure returns (bytes32 digest) {
        uint256 size = (0x20 * 11) + (0x14 * 4) + buyRequest.sellOrderSignature.length;
        bytes memory array = new bytes(size);
        uint256 index;
        assembly {
            index := add(array, 0x20)
        }

        index = AssemblyUtils.writeUint256(index, buyRequest.tokenID);
        index = AssemblyUtils.writeUint256(index, buyRequest.quantity);
        index = AssemblyUtils.writeUint256(index, buyRequest.sellOrderSupply);
        index = AssemblyUtils.writeUint256(index, buyRequest.sellOrderPrice);
        index = AssemblyUtils.writeUint256(index, buyRequest.enableMakeOffer);
        index = AssemblyUtils.writeUint256(index, buyRequest.tokenType);
        index = AssemblyUtils.writeUint256(index, buyRequest.partnerType);
        index = AssemblyUtils.writeUint256(index, buyRequest.partnerFee);
        index = AssemblyUtils.writeUint256(index, buyRequest.transactionType);
        index = AssemblyUtils.writeUint256(index, buyRequest.shippingFee);
        index = AssemblyUtils.writeUint256(index, buyRequest.expiredAt);

        index = AssemblyUtils.writeAddress(index, buyRequest.creator);
        index = AssemblyUtils.writeAddress(index, buyRequest.receiver);
        index = AssemblyUtils.writeAddress(index, buyRequest.tokenAddress);
        index = AssemblyUtils.writeAddress(index, buyRequest.contractAddress);

        index = AssemblyUtils.writeBytes(index, buyRequest.sellOrderSignature);

        assembly {
            digest := keccak256(add(array, 0x20), size)
        }
    }
}
