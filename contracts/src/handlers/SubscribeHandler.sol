// SPDX-License-Identifier: MIT

pragma solidity ^0.8.0;

import {Upgradeable} from "../common/Upgradeable.sol";
import {IERC20} from "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import {SafeERC20} from "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import {ECDSA} from "@openzeppelin/contracts/utils/cryptography/ECDSA.sol";
import {MessageHashUtils} from "@openzeppelin/contracts/utils/cryptography/MessageHashUtils.sol";

struct SubscriptionPayment {
    address payer;
    address recipient;
    address token;
    uint256 amount;
    bytes _id;
}

contract NFTSubscribeHandler is Upgradeable {
    using SafeERC20 for IERC20;

    event SubscriptionPaymentExecuted(bytes _id);

    function executeSubscriptionPayment(
        address payer,
        address recipient,
        address token,
        uint256 amount,
        bytes calldata _id,
        bytes calldata signature
    ) external {
        SubscriptionPayment memory payment = SubscriptionPayment(payer, recipient, token, amount, _id);

        if (msg.sender != payer) {
            require(adminList[msg.sender], "SubscribeHandler: Only admin");
        }

        require(adminList[recipient], "SubscribeHandler: Only admin can receive");
        bytes32 digest = MessageHashUtils.toEthSignedMessageHash(hash(payment));
        address recoveredAddr = ECDSA.recover(digest, signature);

        require(signers[recoveredAddr], "SubscribeHandler: Only admin's signature");

        IERC20(token).safeTransferFrom(payer, recipient, amount);

        emit SubscriptionPaymentExecuted(_id);
    }

    /**
     * @dev Hash the SubscriptionPayment data.
     */
    function hash(SubscriptionPayment memory payment) private pure returns (bytes32 digest) {
        return keccak256(abi.encodePacked(payment.payer, payment.recipient, payment.token, payment.amount, payment._id));
    }
}
