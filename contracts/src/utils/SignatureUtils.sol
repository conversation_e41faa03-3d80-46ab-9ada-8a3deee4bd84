// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import {ECDSA} from "@openzeppelin/contracts/utils/cryptography/ECDSA.sol";
import {MessageHashUtils} from "@openzeppelin/contracts/utils/cryptography/MessageHashUtils.sol";

library SignatureUtils {
    function isValid(
        bytes32 hashed,
        address signer,
        bytes calldata signature
    ) internal pure returns (address, bool) {
        bytes32 digest = MessageHashUtils.toEthSignedMessageHash(hashed);

        address recoverAddr = ECDSA.recover(digest, signature);
        return (recoverAddr, recoverAddr == signer);
    }
}
