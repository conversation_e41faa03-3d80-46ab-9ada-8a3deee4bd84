// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import {ECDSA} from "@openzeppelin/contracts/utils/cryptography/ECDSA.sol";
import {MessageHashUtils} from "@openzeppelin/contracts/utils/cryptography/MessageHashUtils.sol";
import {AssemblyUtils} from "../utils/AssemblyUtils.sol";

contract TestSignatureUtils {
    struct NftBuyRequest {
        uint256 tokenID; // NFT tokenID
        uint256 quantity; // NFT number of copies
        uint256 sellOrderSupply; // quantity for sell order
        uint256 sellOrderPrice; // sell order listing price
        uint256 enableMakeOffer; // offer mode status
        uint256 tokenType; // ERC1155: 0, ERC721: 1
        uint256 partnerType;
        uint256 partnerFee;
        uint256 transactionType;
        uint256 shippingFee;
        address creator; // NFT creator
        address receiver;
        address tokenAddress; // payment token address
        address contractAddress; // collection address of NFT
        bytes sellOrderSignature;
    }

    function hash(NftBuyRequest memory buyRequest)
        public
        pure
        returns (bytes32 digest)
    {
        uint256 size = (0x20 * 10) +
            (0x14 * 4) +
            buyRequest.sellOrderSignature.length;
        bytes memory array = new bytes(size);
        uint256 index;
        assembly {
            index := add(array, 0x20)
        }

        index = AssemblyUtils.writeUint256(index, buyRequest.tokenID);
        index = AssemblyUtils.writeUint256(index, buyRequest.quantity);
        index = AssemblyUtils.writeUint256(index, buyRequest.sellOrderSupply);
        index = AssemblyUtils.writeUint256(index, buyRequest.sellOrderPrice);
        index = AssemblyUtils.writeUint256(index, buyRequest.enableMakeOffer);
        index = AssemblyUtils.writeUint256(index, buyRequest.tokenType);
        index = AssemblyUtils.writeUint256(index, buyRequest.partnerType);
        index = AssemblyUtils.writeUint256(index, buyRequest.partnerFee);
        index = AssemblyUtils.writeUint256(index, buyRequest.transactionType);
        index = AssemblyUtils.writeUint256(index, buyRequest.shippingFee);

        index = AssemblyUtils.writeAddress(index, buyRequest.creator);
        index = AssemblyUtils.writeAddress(index, buyRequest.receiver);
        index = AssemblyUtils.writeAddress(index, buyRequest.tokenAddress);
        index = AssemblyUtils.writeAddress(index, buyRequest.contractAddress);

        index = AssemblyUtils.writeBytes(index, buyRequest.sellOrderSignature);

        assembly {
            digest := keccak256(add(array, 0x20), size)
        }
    }

    function isValid(
        bytes32 hashed,
        address signer,
        bytes calldata signature
    ) public pure returns (address, bool) {
        bytes32 digest = MessageHashUtils.toEthSignedMessageHash(hashed);

        address recoverAddr = ECDSA.recover(digest, signature);
        return (recoverAddr, recoverAddr == signer);
    }
}
