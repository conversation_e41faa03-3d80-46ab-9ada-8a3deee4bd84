// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import {SafeERC20} from "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import {IERC20} from "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import {ECDSA} from "@openzeppelin/contracts/utils/cryptography/ECDSA.sol";
import {MessageHashUtils} from "@openzeppelin/contracts/utils/cryptography/MessageHashUtils.sol";

import {Upgradeable} from "../common/Upgradeable.sol";

/**
 * @dev Structure of FeeTransfer
 * @notice using for calculating the fee for each criteria
 */
struct FeeTransfer {
    uint256 transactionType;
    uint256 transferAmount;
    uint256 ratio;
    address paymentToken;
    address from;
    address to;
}

// Payout Group
struct PayoutGroup {
    uint256 storeFeeRatio;
    address storeAddress;
    address artistAddress;
    address[] payoutAddress;
    uint256[] payoutRatios;
}

contract NFTFeeUtils is Upgradeable {
    using SafeERC20 for IERC20;

    address private constant ZERO_ADDRESS = address(0);
    uint256 private constant NORMAL_TRANSACTION = 0;
    uint256 private constant FIAT_PAYMENT = 1;
    uint256 private constant GASLESS_TRANSACTION = 2;
    uint256 private constant NOT_PARTNER = 0;

    uint256 private constant MAX_UINT = type(uint256).max;

    function _transfer(
        address from,
        address to,
        address paymentToken,
        uint256 fee
    ) private {
        IERC20(paymentToken).safeTransferFrom(from, to, fee);
    }

    function _transferFee(
        FeeTransfer memory feeTransfer
    ) private returns (uint256 fee) {
        // * [ratio > 0]
        if (feeTransfer.ratio > 0) {
            // * [fee = transferAmount * ratio]
            fee = (feeTransfer.transferAmount * feeTransfer.ratio) / 1e4;

            if (feeTransfer.paymentToken == ZERO_ADDRESS) {
                (bool success, ) = payable(feeTransfer.to).call{value: fee}("");
                require(success, "FeeUtils: fail to transfer fee");
            } else {
                if (
                    feeTransfer.transactionType == NORMAL_TRANSACTION ||
                    feeTransfer.transactionType == FIAT_PAYMENT
                ) {
                    _transfer(
                        msg.sender,
                        feeTransfer.to,
                        feeTransfer.paymentToken,
                        fee
                    );
                } else {
                    // GASLESS_TRANSACTION or not apply at all
                    _transfer(
                        feeTransfer.from,
                        feeTransfer.to,
                        feeTransfer.paymentToken,
                        fee
                    );
                }
            }
        }
    }

    /**
     * @dev Buy from primary sale
     * @param data: [0] tokenID, [1] quantity, [2] sellOrderSupply, [3] sellOrderPrice, [4] enableMakeOffer
     * -------------[5] buyingAmount, [6] tokenType, [7] partnerType, [8] partnerFee, [9] transactionType,
     * -------------[10] storeFeeRatio, [11] shippingFee, [12] expiredAt, [13-...] payoutRatios
     * @param addr: [0] creator == artist, [1] tokenAddress, [2] collectionAddress, [3] signer, [4] storeAddress,
     * -------------[5] receiver, [6---] payoutAddress
     * @param signatures: [0] nftBuyRequestSignture, [1] sellOrderSignature, [2] payoutSignature
     */
    function buyNowNative(
        uint256[] memory data,
        address[] memory addr,
        string[] memory,
        bytes[] memory signatures
    ) public payable {
        uint256 transferAmount = data[3] * data[5];
        uint256 shippingFee = data[11];

        FeeTransfer memory feeTransfer = FeeTransfer({
            transactionType: data[9],
            transferAmount: shippingFee,
            ratio: 1e4,
            paymentToken: addr[1],
            from: addr[5],
            to: addr[4]
        });

        if (feeTransfer.paymentToken == ZERO_ADDRESS) {
            // ? CHECK: msg.value must equal to `transferAmount`
            require(
                msg.value == transferAmount + shippingFee,
                "FeeUtils: invalid transfer amount"
            );
        }

        // ? Transfer shipping fee
        if(feeTransfer.transferAmount > 0) {
            _transferFee(feeTransfer);
        }

        // ? Transfer ADMIN fee
        feeTransfer.transferAmount = transferAmount;
        feeTransfer.to = recipient;
        if (data[7] == NOT_PARTNER) {
            feeTransfer.ratio = tokensFee[feeTransfer.paymentToken];
        } else {
            feeTransfer.ratio = data[8];
        }

        uint256 adminFee;
        if(feeTransfer.transferAmount > 0) {
            adminFee = _transferFee(feeTransfer);
        }

        // ? Transfer STORE FEE
        feeTransfer.ratio = data[10];
        feeTransfer.to = addr[4];

        uint256 storeFee;
        if(feeTransfer.transferAmount > 0) {
            storeFee = _transferFee(feeTransfer);
        }

        feeTransfer.transferAmount -= adminFee;
        feeTransfer.transferAmount -= storeFee;

        // ? Transfer the remaining amount to payout group or seller
        if (data.length > 13) {
            // TODO: Check payout group
            uint256[] memory payoutRatios = new uint256[](data.length - 13);
            address[] memory payoutAddress = new address[](payoutRatios.length);

            for (uint256 idx = 13; idx < data.length; idx++) {
                payoutRatios[idx - 13] = data[idx];
                payoutAddress[idx - 13] = addr[idx - 7];
            }

            PayoutGroup memory payoutGroup = PayoutGroup({
                storeFeeRatio: data[10],
                storeAddress: addr[4],
                artistAddress: addr[0],
                payoutRatios: payoutRatios,
                payoutAddress: payoutAddress
            });
            address signer = addr[3];
            require(
                ECDSA.recover(
                    MessageHashUtils.toEthSignedMessageHash(hash(payoutGroup)),
                    signatures[2]
                ) == signer,
                "FeeUtils: invalid payout group"
            );

            for (uint256 i = 0; i < payoutGroup.payoutAddress.length; i++) {
                feeTransfer.ratio = payoutGroup.payoutRatios[i];
                feeTransfer.to = payoutGroup.payoutAddress[i];

                _transferFee(feeTransfer);
            }
        } else if (feeTransfer.transferAmount > 0) {
            feeTransfer.to = addr[0];
            feeTransfer.ratio = 1e4;
            _transferFee(feeTransfer);
        }
    }

    /**
     * @dev Buy from secondary sale
     * @param data: [0] tokenID, [1] royaltyRatio, [2] sellOrderSupply, [3] sellOrderPrice, [4] enableMakeOffer,
     * ------[5] amount, [6] tokenType, [7] partnerType, [8] partnerFee, [9] transactionType,
     * ------[10] storeFeeRatio, [11-...] payoutRatios
     * @param addr: [0] creator == artist, [1] contractAddress, [2] tokenAddress, [3] seller, [4] signer,
     * ------[5] storeAddress, [6] receiver, [7---] payoutAddress
     */
    function sellNowNative(
        uint256[] memory data,
        address[] memory addr,
        string[] memory,
        bytes[] memory
    ) public payable {
        uint256 transferAmount = data[3] * data[5];
        FeeTransfer memory feeTransfer = FeeTransfer({
            transactionType: data[9],
            transferAmount: transferAmount,
            ratio: 0,
            paymentToken: addr[2],
            from: addr[6],
            to: recipient
        });

        // TODO: Check payment token
        if (feeTransfer.paymentToken == ZERO_ADDRESS) {
            // ? CHECK: msg.value must equal to `transferAmount`
            require(
                msg.value == feeTransfer.transferAmount,
                "FeeUtils: invalid transfer amount"
            );
        }

        // TODO: Handle fee transfers

        // ? Transfer ADMIN fee
        if (data[7] == NOT_PARTNER) {
            feeTransfer.ratio = tokensFee[feeTransfer.paymentToken];
        } else {
            feeTransfer.ratio = data[8];
        }

        uint256 adminFee = _transferFee(feeTransfer);

        // ? Transfer STORE FEE
        feeTransfer.ratio = data[10];
        feeTransfer.to = addr[5];
        uint256 storeFee = _transferFee(feeTransfer);

        // ? Transfer ROYALTY fee
        feeTransfer.ratio = data[1];
        feeTransfer.to = addr[0];
        uint256 royaltyFee = _transferFee(feeTransfer);

        feeTransfer.transferAmount -= adminFee;
        feeTransfer.transferAmount -= storeFee;
        feeTransfer.transferAmount -= royaltyFee;

        // ? Transfer the remaining amount
        if (feeTransfer.transferAmount > 0) {
            feeTransfer.to = addr[3];
            feeTransfer.ratio = 1e4;
            _transferFee(feeTransfer);
        }
    }

    /**
     * @dev Accept offer
     * @param data: [0] tokenID, [1] quantity, [2] sellOrderSupply, [3] enableMakeOffer, [4] sellOrderPrice
     * -------------[5] offerAmount, [6] offerPrice, [7] listingTime, [8] expirationTime, [9] tokenType,
     * -------------[10] partnerType, [11] partnerFee, [12] storeFeeRatio, [13-...] payoutRatio
     * @param addr: [0] creator == artist, [1] contractAddress, [2] tokenAddress, [3] receiver, [4] signer,
     * -------------[5] storeAddress, [6-...] payoutAddress
     * @param signatures: [0] nftAcceptOfferSignature, [1] sellOrderSignature, [2] makeOfferSignature, [3] payoutSignature
     */
    function acceptOffer(
        uint256[] memory data,
        address[] memory addr,
        string[] memory,
        bytes[] memory signatures
    ) public {
        uint256 transferAmount = data[5] * data[6];

        FeeTransfer memory feeTransfer = FeeTransfer({
            transactionType: MAX_UINT,
            transferAmount: transferAmount,
            ratio: 0,
            paymentToken: addr[2],
            from: addr[3],
            to: recipient
        });

        // TODO: Handle fee transfers

        // ? Transfer ADMIN fee
        if (data[10] == NOT_PARTNER) {
            feeTransfer.ratio = tokensFee[feeTransfer.paymentToken];
        } else {
            feeTransfer.ratio = data[11];
        }

        uint256 adminFee = _transferFee(feeTransfer);

        // ? Transfer STORE FEE
        feeTransfer.ratio = data[12];
        feeTransfer.to = addr[5];

        uint256 storeFee = _transferFee(feeTransfer);

        feeTransfer.transferAmount -= adminFee;
        feeTransfer.transferAmount -= storeFee;

        // ? Transfer the remaining amount to payout group or seller
        if (data.length > 13) {
            // TODO: Check payout group
            uint256[] memory payoutRatios = new uint256[](data.length - 13);
            address[] memory payoutAddress = new address[](payoutRatios.length);

            for (uint256 idx = 13; idx < data.length; idx++) {
                payoutRatios[idx - 13] = data[idx];
                payoutAddress[idx - 13] = addr[idx - 7];
            }

            PayoutGroup memory payoutGroup = PayoutGroup({
                storeFeeRatio: data[12],
                storeAddress: addr[5],
                artistAddress: addr[0],
                payoutRatios: payoutRatios,
                payoutAddress: payoutAddress
            });
            address signer = addr[4];
            require(
                ECDSA.recover(
                    MessageHashUtils.toEthSignedMessageHash(hash(payoutGroup)),
                    signatures[3]
                ) == signer,
                "FeeUtils: invalid payout group"
            );

            for (uint256 i = 0; i < payoutGroup.payoutAddress.length; i++) {
                feeTransfer.ratio = payoutGroup.payoutRatios[i];
                feeTransfer.to = payoutGroup.payoutAddress[i];

                _transferFee(feeTransfer);
            }
        } else if (feeTransfer.transferAmount > 0) {
            feeTransfer.to = addr[0];
            feeTransfer.ratio = 1e4;
            _transferFee(feeTransfer);
        }
    }

    function hash(
        PayoutGroup memory payoutGroup
    ) internal pure returns (bytes32 digest) {
        digest = keccak256(
            abi.encodePacked(
                payoutGroup.storeFeeRatio,
                payoutGroup.storeAddress,
                payoutGroup.artistAddress,
                payoutGroup.payoutAddress,
                payoutGroup.payoutRatios
            )
        );
    }
}
