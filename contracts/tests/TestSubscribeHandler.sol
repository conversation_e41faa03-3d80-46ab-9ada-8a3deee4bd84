// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import {ECDSA} from "@openzeppelin/contracts/utils/cryptography/ECDSA.sol";
import {MessageHashUtils} from "@openzeppelin/contracts/utils/cryptography/MessageHashUtils.sol";

contract TestSubscribeHandler {
    address signer;
    mapping(address => bool) isAdmin;

    constructor(address _signer, address recipient) {
        signer = _signer;
        isAdmin[recipient] = true;
    }

    // @Test Verify SubscriptionPayment object.
    function verifySignature(
        address recipient,
        address token,
        uint256 amount,
        bytes calldata _id,
        bytes calldata signature
    ) external view returns (address recoverAddr, bool result) {
        bytes32 digest = MessageHashUtils.toEthSignedMessageHash(
            hash(recipient, token, amount, _id)
        );

        recoverAddr = ECDSA.recover(digest, signature);

        return (recoverAddr, recoverAddr == signer);
    }

    // @Test Hash the SubscriptionPayment data.
    function hash(
        address recipient,
        address token,
        uint256 amount,
        bytes calldata _id
    ) public pure returns (bytes32 digest) {
        return keccak256(abi.encodePacked(recipient, token, amount, _id));
    }
}
