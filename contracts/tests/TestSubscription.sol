// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

contract TestSubscription {
    event SubscriptionPaymentExecuted(bytes _id);

    function generateRandomString(
        uint256 length
    ) public view returns (bytes memory) {
        bytes memory randomBytes = new bytes(length);
        for (uint i = 0; i < length; i++) {
            randomBytes[i] = bytes1(
                uint8(
                    uint256(
                        keccak256(
                            abi.encodePacked(
                                block.timestamp,
                                block.difficulty,
                                i
                            )
                        )
                    )
                )
            );
        }
        return randomBytes;
    }

    function emitEvent() external {
        bytes memory _id = generateRandomString(12);

        emit SubscriptionPaymentExecuted(_id);
    }
}
