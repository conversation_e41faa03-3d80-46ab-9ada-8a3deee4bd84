// SPDX-License-Identifier: MIT
pragma solidity ^0.8.0;

import {SignatureUtils} from "../src/utils/SignatureUtils.sol";

contract TestFeeUtils {
    address signer;

    constructor(address _signer) {
        signer = _signer;
    }

    function verifyPayoutGroup(
        uint256 storeFeeRatio,
        address storeAddr,
        address artistAddr,
        address[] calldata payouts,
        uint256[] calldata ratios,
        bytes calldata signature
    ) external view returns (address recoverAddr, bool result) {
        bytes32 hashed = hash(
            storeFeeRatio,
            storeAddr,
            artistAddr,
            payouts,
            ratios
        );

        return SignatureUtils.isValid(hashed, signer, signature);
    }

    function hash(
        uint256 storeFeeRatio,
        address storeAddr,
        address artistAddr,
        address[] calldata payouts,
        uint256[] calldata ratios
    ) public pure returns (bytes32 hashed) {
        return
            keccak256(
                abi.encodePacked(
                    storeFeeRatio,
                    storeAddr,
                    artistAddr,
                    payouts,
                    ratios
                )
            );
    }
}
