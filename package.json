{"name": "blockchain-contracts", "version": "1.0.0", "description": "", "main": "truffle-config.js", "directories": {"test": "test"}, "dependencies": {"@openzeppelin/contracts": "^5.0.2", "@pancakeswap/pancake-swap-lib": "^0.0.4", "abi-decoder": "^2.4.0", "chai": "^4.3.4", "dotenv": "^10.0.0", "ethereum-waffle": "^3.4.0", "ethereumjs-abi": "^0.6.8", "ethereumjs-util": "^7.1.5", "ethers": "^6.7.0", "fs-extra": "^10.1.0", "hardhat": "^2.6.6", "node-cmd": "^5.0.0", "web3": "^1.10.2"}, "devDependencies": {"@gelatonetwork/relay-context": "^3.2.0", "@gelatonetwork/relay-sdk": "^5.2.0", "@nomicfoundation/hardhat-chai-matchers": "^2.1.0", "@nomicfoundation/hardhat-ethers": "^3.0.4", "@nomicfoundation/hardhat-network-helpers": "^1.0.12", "@nomicfoundation/hardhat-verify": "^1.0.0", "@nomiclabs/hardhat-etherscan": "^3.1.0", "@nomiclabs/hardhat-web3": "^2.0.0", "@openzeppelin/hardhat-upgrades": "^2.2.1", "@typechain/ethers-v6": "^0.5.0", "@typechain/hardhat": "^9.0.0", "@types/mocha": "^10.0.10", "@types/node": "^24.1.0", "ts-node": "^10.9.1", "typescript": "^5.8.3"}, "scripts": {"test": "npx hardhat test", "test:race-conditions": "npx hardhat test test/race-condition-buy.test.ts", "compile": "npx hardhat compile", "deploy:buy:mumbai": "HARDHAT_NETWORK=mumbai node scripts/deploy.js BUY_HANDLER", "deploy:buy:rinkeby": "HARDHAT_NETWORK=rinkeby node scripts/deploy.js BUY_HANDLER", "deploy:buy:bsctestnet": "HARDHAT_NETWORK=bsctestnet node scripts/deploy.js BUY_HANDLER", "deploy:sell:mumbai": "HARDHAT_NETWORK=mumbai node scripts/deploy.js SELL_HANDLER", "deploy:sell:rinkeby": "HARDHAT_NETWORK=rinkeby node scripts/deploy.js SELL_HANDLER", "deploy:sell:bsctestnet": "HARDHAT_NETWORK=bsctestnet node scripts/deploy.js SELL_HANDLER", "deploy:offer:mumbai": "HARDHAT_NETWORK=mumbai node scripts/deploy.js OFFER_HANDLER", "deploy:offer:rinkeby": "HARDHAT_NETWORK=rinkeby node scripts/deploy.js OFFER_HANDLER", "deploy:offer:bsctestnet": "HARDHAT_NETWORK=bsctestnet node scripts/deploy.js OFFER_HANDLER", "deploy:cancel:mumbai": "HARDHAT_NETWORK=mumbai node scripts/deploy.js CANCEL_HANDLER", "deploy:cancel:rinkeby": "HARDHAT_NETWORK=rinkeby node scripts/deploy.js CANCEL_HANDLER", "deploy:cancel:bsctestnet": "HARDHAT_NETWORK=bsctestnet node scripts/deploy.js CANCEL_HANDLER", "deploy:airdrop:mumbai": "HARDHAT_NETWORK=mumbai node scripts/deploy.js AIRDROP_HANDLER", "deploy:airdrop:rinkeby": "HARDHAT_NETWORK=rinkeby node scripts/deploy.js AIRDROP_HANDLER", "deploy:airdrop:bsctestnet": "HARDHAT_NETWORK=bsctestnet node scripts/deploy.js AIRDROP_HANDLER", "deploy:exchange:mumbai": "HARDHAT_NETWORK=mumbai node scripts/deploy.js EXCHANGE", "deploy:exchange:rinkeby": "HARDHAT_NETWORK=rinkeby node scripts/deploy.js EXCHANGE", "deploy:exchange:bsctestnet": "HARDHAT_NETWORK=bsctestnet node scripts/deploy.js EXCHANGE", "deploy:fee:mumbai": "HARDHAT_NETWORK=mumbai node scripts/deploy.js FEE_UTILS", "deploy:fee:rinkeby": "HARDHAT_NETWORK=rinkeby node scripts/deploy.js FEE_UTILS", "deploy:fee:bsctestnet": "HARDHAT_NETWORK=bsctestnet node scripts/deploy.js FEE_UTILS", "deploy:box:mumbai": "HARDHAT_NETWORK=mumbai node scripts/deploy.js BOX_UTILS", "deploy:box:rinkeby": "HARDHAT_NETWORK=rinkeby node scripts/deploy.js BOX_UTILS", "deploy:box:bsctestnet": "HARDHAT_NETWORK=bsctestnet node scripts/deploy.js BOX_UTILS", "deploy:meta:mumbai": "HARDHAT_NETWORK=mumbai node scripts/deploy.js META_HANDLER 0x9Fc3C76824c73d4302F2A21388ff17e0255EDf1B", "deploy:meta:rinkeby": "HARDHAT_NETWORK=rinkeby node scripts/deploy.js META_HANDLER 0x98bF4169a32C76c8F6e11bdA508F77fb1a892D01", "deploy:meta:bsctestnet": "HARDHAT_NETWORK=bsctestnet node scripts/deploy.js META_HANDLER 0xE4c5B47fd7f853be9a9e11252B3C62644155406c", "deploy:721:mumbai": "HARDHAT_NETWORK=mumbai node scripts/deploy.js NFTIFY721", "deploy:721:rinkeby": "HARDHAT_NETWORK=rinkeby node scripts/deploy.js NFTIFY721", "deploy:721:bsctestnet": "HARDHAT_NETWORK=bsctestnet node scripts/deploy.js NFTIFY721", "deploy:1155:mumbai": "HARDHAT_NETWORK=mumbai node scripts/deploy.js NFTIFY1155", "deploy:1155:rinkeby": "HARDHAT_NETWORK=rinkeby node scripts/deploy.js NFTIFY1155", "deploy:1155:bsctestnet": "HARDHAT_NETWORK=bsctestnet node scripts/deploy.js NFTIFY1155"}, "author": "", "license": "ISC"}