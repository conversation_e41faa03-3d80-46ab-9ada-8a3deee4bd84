// import { CallWithSyncFeeERC2771Request, CallWithERC2771Request } from "@gelatonetwork/relay-sdk";
import { CallWithERC2771Request, ERC2771Type, GelatoRelay } from "@gelatonetwork/relay-sdk";
import { ethers, network } from "hardhat";
// import { Contract, Signer, Wallet } from "ethers";
import { SAVE_PATH, ADMIN, RECIPIENT, SIGNER, ERC_721, ERC_1155 } from "../utils/constant";
import { NFTExchangeV1, NFTExchangeProxy, BurnRedeemHandler, N1Meta } from "../../typechain-types";
import { getContract } from "../utils/helper";
import "dotenv/config";

async function main() {
  const relay = new GelatoRelay();
  // Set up on-chain variables, such as target address
  const wallet = new ethers.Wallet(process.env.DEV_KEY as string, ethers.provider);
  const signer = await ethers.provider.getSigner();
  const user = await wallet.getAddress();
  // console.log(`signer: ${user}`);

  // Generate the target payload
  const contract: N1Meta = await ethers.getContractAt("N1Meta", "******************************************", signer);
  const { data } = await contract.mint100.populateTransaction();

  console.log(`data: ${data}`);

  // Populate a relay request
  const request: CallWithERC2771Request = {
    chainId: BigInt((await ethers.provider.getNetwork()).chainId),
    target: await contract.getAddress(),
    data: data ?? "",
    user: user,
  };

  // Without a specific API key, the relay request will fail!
  // Go to https://relay.gelato.network to get a testnet API key with 1Balance.
  // Send a relay request using Gelato Relay!
  const relayResponse = await relay.sponsoredCallERC2771(
    request,
    wallet,
    "eTPSY_rNRsZ8cgzzlO86taus81HdNI35MywjeCGUd2M_"
  );
  console.log(relayResponse);

  // const sig = await relay.getSignatureDataERC2771(request, wallet, ERC2771Type.SponsoredCall);
  // console.log(
  //   `signature: ${JSON.stringify(
  //     sig,
  //     (key, value) => (typeof value === "bigint" ? value.toString() : value), // return everything else unchanged
  //     2
  //   )}`
  // );

  // const relayResponseWithSignature = await relay.sponsoredCallERC2771WithSignature(
  //   sig.struct,
  //   sig.signature,
  //   "eTPSY_rNRsZ8cgzzlO86taus81HdNI35MywjeCGUd2M_"
  // );
  // console.log(relayResponseWithSignature);
}

// We recommend this pattern to be able to use async/await everywhere
// and properly handle errors.
main().catch((error) => {
  console.error(error);
  process.exitCode = 1;
});
