import { ethers } from "hardhat";
import {
  deployContract,
  deployMultiContract,
  deployContractUpgradeable,
  deployMultiContractUpgradeable,
} from "../utils/contract";
import { getContract, getBlockTimeStamp, createSignature } from "../utils/helper";
import { SUNTORY_SAVE_PATH, ADMIN, RECIPIENT, SIGNER, ERC_721, ERC_1155 } from "../utils/constant";
import { NFTExchangeV1, NFTExchangeProxy } from "../../typechain-types";
import { ItemsStruct } from "../../typechain-types/contracts/src/exchanges/NFTExchangeV1";
// import { Mixed } from "web3/utils";
import { promises as fsPromises } from "fs";

const _data_721 = [
  [
    "0x6109acb962cc333775283c5ace05b26c59e253f57416a31e95f8a91c0761a40c",
    "1",
    "1",
    "2500000000000000",
    "1",
    "1",
    "1",
    "1",
    "600",
    "0",
    "0",
    "10000"
],
[
  "******************************************",
  "******************************************",
  "******************************************",
  "0xE7436132C68625207aBB9fEe888d4ee4eae2b6e3",
  "******************************************",
  "0xb30FF60Fc0D5556A78bB15F7B65c9bbA83F1C343",
  "******************************************"
],
[
  "660546f2a56a3bc08edb5dd9"
],
[
  "0x420cbeb3903288c1e37df5d4555f90d698900dad2c20e7de5f5036c2e4b229ff2204e322b7b51dfa9eed68d31ef2d32d45bd4939d34b8f59d738fa13d297d36e1c",
  "0x00eff8c7a95d8e14386d333f6f16fd5443f8cc358b92bd608b303d9fa1d659b970ad24784f2d9d03c53127751cb560d0e3d718842574a013edd08a8b7051a7e81b",
  "0xbdfb5e87197d2f3837af8ae6449054e03a479d8f6d0428b7e77775ed50840e1f7bb551c9ebcd8b897722f7d97a3d99fb5158b341a23bea45e1ba2b01d05b5f3e1c"
]
]

async function main() {
  const [deployer] = await ethers.getSigners();
  console.log("deployer: ", deployer.address);

  const _NFTExchangeProxy = await getContract("NFTExchangeProxy", SUNTORY_SAVE_PATH);

  let _NFTExchangeV1 = await getContract("NFTExchangeV1", SUNTORY_SAVE_PATH);

  _NFTExchangeV1 = _NFTExchangeV1.attach(_NFTExchangeProxy.target);

  const signer = new ethers.Wallet("a3911655f6890a4ece9cb18f5c394bff1fd2cf7f10e9b874f434cd5e68a03ffe")
  console.log("signer: ", signer.address);

  const totalPrice = "0.0001"
  const nftBuyRequestSignture = await createSignature(
    [
        _data_721[0][0],
        _data_721[0][1],
        _data_721[0][2],
        _data_721[0][3],
        _data_721[0][4],
        _data_721[0][6],
        _data_721[0][7],
        _data_721[0][8],
        _data_721[0][9],
        _data_721[1][0],
        _data_721[1][5],
        _data_721[1][1],
        _data_721[1][2],
        _data_721[3][1],
    ],
    [
        "uint256",
        "uint256",
        "uint256",
        "uint256",
        "uint256",
        "uint256",
        "uint256",
        "uint256",
        "uint256",
        "address",
        "address",
        "address",
        "address",
        "bytes"
    ],
    signer
  )

  console.log(nftBuyRequestSignture);
  _data_721[3][0] = nftBuyRequestSignture

  let waiting = await (_NFTExchangeV1 as NFTExchangeV1).buyNowNative(
    _data_721[0],
    _data_721[1],
    _data_721[2],
    _data_721[3],
    {value: "2500000000000000"}
  );
  await waiting.wait();
  console.log(`tx: ${waiting.hash}`);
}

// We recommend this pattern to be able to use async/await everywhere
// and properly handle errors.
main().catch((error) => {
  console.error(error);
  process.exitCode = 1;
});
