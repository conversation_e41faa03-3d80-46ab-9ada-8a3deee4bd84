import { ethers } from "hardhat";
import {
  deployContract,
  deployMultiContract,
  deployContractUpgradeable,
  deployMultiContractUpgradeable,
} from "../utils/contract";
import { getContract, getBlockTimeStamp, createSignature } from "../utils/helper";
import { SUNTORY_SAVE_PATH, ADMIN, RECIPIENT, SIGNER, ERC_721, ERC_1155 } from "../utils/constant";
import { NFTExchangeV1, NFTExchangeProxy } from "../../typechain-types";
import { ItemsStruct } from "../../typechain-types/contracts/src/exchanges/NFTExchangeV1";
// import { Mixed } from "web3/utils";
import { promises as fsPromises } from "fs";

async function main() {
  const [deployer] = await ethers.getSigners();
  console.log("deployer: ", deployer.address);

  const token_address = "******************************************"

  const _NFTExchangeProxy = await getContract("NFTExchangeProxy", SUNTORY_SAVE_PATH);

  let _NFTExchangeV1 = await getContract("NFTExchangeV1", SUNTORY_SAVE_PATH);

  _NFTExchangeV1 = _NFTExchangeV1.attach(_NFTExchangeProxy.target);

  let waiting = await (_NFTExchangeV1 as NFTExchangeV1).tokensFee(token_address);
  console.log(`Token fee: ${waiting.toString()}`);
}

// We recommend this pattern to be able to use async/await everywhere
// and properly handle errors.
main().catch((error) => {
  console.error(error);
  process.exitCode = 1;
});
