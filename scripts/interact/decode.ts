import { ethers } from "hardhat";
import {
  deployContract,
  deployMultiContract,
  deployContractUpgradeable,
  deployMultiContractUpgradeable,
} from "../utils/contract";
import { SAVE_PATH, ADMIN, RECIPIENT, SIGNER } from "../utils/constant";
import { NFTExchangeV1, NFTExchangeProxy } from "../../typechain-types";

async function main() {
  const [deployer] = await ethers.getSigners();
  console.log("signer: ", deployer.address);
  // let iface = new ethers.Interface(["function buyNowNative(uint256[] data,address[] addr,string[] strs,bytes[] signatures)"]);

  // let y = iface.parseTransaction({data: "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"});

  // let iface = new ethers.Interface(["function openBox(uint256[] data,address[] addr,string[] strs,bytes[] signatures)"]);

  // let y = iface.parseTransaction({data: "0x295e3051000000000000000000000000000000000000000000000000000000000000008000000000000000000000000000000000000000000000000000000000000000e0000000000000000000000000000000000000000000000000000000000000018000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000c000000000000000000000000000000000000000000000000000000000000001b0000000000000000000000000000000000000000000000000000000000000004000000000000000000000000b30ff60fc0d5556a78bb15f7b65c9bba83f1c343000000000000000000000000e7436132c68625207abb9fee888d4ee4eae2b6e300000000000000000000000079f04e2ec735bc6e75e5225fc94171f7f440010a000000000000000000000000f88c4df6d56011bf0c2c3014d1b11e25055c0f1900000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000000231320000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000004000000000000000000000000000000000000000000000000000000000000000c00000000000000000000000000000000000000000000000000000000000000041e6c8c21a14855adef1d6936d9d2fdadfd25c7150f2e44b71aab76e49ea7d8773299b0e39b51f722d5c5c19c453d675335b9e4f0b641c2a554a8f5c68b450c7d41b00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000004118923e3557dacee864634b344b8aea1ef0a23fe30ccd166ce4db03c3860bdc042f5e485d7008ac7730ef8f6daae43f5cfc91fb3338f941b3e07178e468a6d3131c00000000000000000000000000000000000000000000000000000000000000"});

  // console.log(y?.args);

  // const c1155 = await ethers.getContractAt("Mother1155", "******************************************");
  // const tx = await c1155.mint("******************************************", 2, 5, "0x");
  // console.log(tx.hash);

  // const c721 = await ethers.getContractAt("Mother721", "******************************************");
  // console.log(`symbol: ${await c721.owner()}`);

  // const tx = await c721.transferFrom(
  //   "******************************************",
  //   "******************************************",
  //   0
  // );
  // console.log(tx.hash);

  //   const hashCollection = ethers.solidityPackedKeccak256(
  //     [
  //       "uint256",
  //       "uint256",
  //       "uint256",
  //       "uint256",
  //       "uint256",
  //       "uint256",
  //       "uint256",
  //       "uint256",
  //       "uint256",
  //       "address",
  //       "address",
  //       "address",
  //       "address",
  //       "bytes"
  //     ],
  //     [
  //       // 115770224146561155622363816781858642349101061631201527741474810466135438086011n,
  //       "0xfff39ff2b577e4b0ea63deffdca9afb5fdbc3649a04fd05cc5cf019fa48d4f7b",
  //       100n,
  //       100n,
  //       1000000000000000000n,
  //       1n,
  //       0n,
  //       0n,
  //       200n,
  //       0n,
  //       "******************************************",
  //       "******************************************",
  //       "******************************************",
  //       "******************************************",
  //       "0x229d87358a03ab58de3762dd271ba01a717f55bdfa4cfd4e4dc42d53de8118296e332ddbce3beaf0e93995d852748c34ab469b5357b41e793930147463ca2e521b",
  //     ],
  //   );

  //   // STEP 2: 32 bytes of data in Uint8Array
  //   const messageHashBinary = ethers.getBytes(hashCollection);

  //   // STEP 3: To sign the 32 bytes of data, make sure you pass in the data
  //   const signature = await deployer.signMessage(messageHashBinary);
  //   console.log(signature);

  // // Your hexadecimal string
  // const hexString = "0x000000000000000000000000000000000000000000000000261eefda6321ef46";

  // // Convert the hexadecimal string to a BigNumber
  // const bigNumber = ethers.toBigInt(hexString);

  // // If you need the number as a string (since it may be too large for JavaScript's Number type)
  // const numberAsString = bigNumber.toString();

  // console.log(`Hexadecimal: ${hexString}`);
  // console.log(`As BigNumber: ${bigNumber}`);
  // console.log(`As string: ${numberAsString}`);
}

// We recommend this pattern to be able to use async/await everywhere
// and properly handle errors.
main().catch((error) => {
  console.error(error);
  process.exitCode = 1;
});
