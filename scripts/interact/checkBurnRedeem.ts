import { ethers } from "hardhat";
import {
  deployContract,
  deployMultiContract,
  deployContractUpgradeable,
  deployMultiContractUpgradeable,
} from "../utils/contract";
import { getContract, getBlockTimeStamp, createSignature } from "../utils/helper";
import { SAVE_PATH, ADMIN, RECIPIENT, SIGNER, ERC_721, ERC_1155 } from "../utils/constant";
import { NFTExchangeV1, NFTExchangeProxy, BurnRedeemHandler } from "../../typechain-types";
import { ItemsStruct } from "../../typechain-types/contracts/src/exchanges/NFTExchangeV1";
// import { Mixed } from "web3/utils";
import { promises as fsPromises } from "fs";

async function main() {
  const [deployer] = await ethers.getSigners();
  console.log("signer: ", deployer.address);

  // checkHash();
  // process.exit();

  const _BurnRedeem = (await getContract("<PERSON><PERSON><PERSON><PERSON>Hand<PERSON>", SAVE_PATH)) as BurnRedeemHandler;

  const _NFTExchangeProxy = await getContract("NFTExchangeProxy", SAVE_PATH);

  let _NFTExchangeV1 = await getContract("NFTExchangeV1", SAVE_PATH);

  _NFTExchangeV1 = _NFTExchangeV1.attach(_NFTExchangeProxy.target);

  const _expireAt = await getBlockTimeStamp();

  const _burnItems = [
    {
      id: 0,
      quantity: 1,
      tokenType: ERC_721,
      collection: SIGNER,
    },
    {
      id: 1,
      quantity: 2,
      tokenType: ERC_1155,
      collection: SIGNER,
    },
  ];
  const _redeemItems = [
    {
      id: 0,
      quantity: 1,
      tokenType: ERC_721,
      collection: SIGNER,
    },
  ];

  // const hash = await _BurnRedeem.hash({
  //   burnItems: _burnItems,
  //   redeemItems: _redeemItems,
  //   receiver: RECIPIENT,
  //   redeemTimes: 1,
  //   totalRedeemTimes: 5,
  //   startAt: 0,
  //   expireAt: _expireAt,
  //   eventId: "0xabc123",
  //   signature: "0x",
  // });

  const hash = await _BurnRedeem.hash({
    burnItems: [
      {
        id: "76922230092905295167948976286653073817408769185613153271066357247105540488968",
        quantity: 1,
        tokenType: 0,
        collection: "******************************************",
      },
      {
        id: "85003561628692812923342894928229009219765207790136449391948836279290031849522",
        quantity: 1,
        tokenType: 0,
        collection: "******************************************",
      },
    ],
    redeemItems: [
      {
        id: "91628078082115406611249698762269260163838683122476090815903466512646985743024",
        quantity: 1,
        tokenType: 0,
        collection: "******************************************",
      },
    ],
    receiver: "******************************************",
    redeemTimes: 1,
    totalRedeemTimes: 100,
    startAt: 1694433609,
    expireAt: 0,
    eventId: "0x64ff0162105c558c0177e9ed",
    signature: "0x",
  });

  console.log(hash);

  // [
  //   {
  //     id: "91628078082115406611249698762269260163838683122476090815903466512646985743024",
  //     quantity: 1,
  //     tokenType: 0,
  //     collection: "******************************************",
  //   },
  // ],
  // [
  //   {
  //     id: "86958057122118894525889288858420100310895216916339539471994696133372654478124",
  //     quantity: 1,
  //     tokenType: 0,
  //     collection: "******************************************",
  //   },
  // ],
  //   "******************************************",
  //   1,
  //   100,
  //   1694492943,
  //   0,
  //   "0x64ffe93290bd29823afcb2ec";

  // let waiting = await (_NFTExchangeV1 as NFTExchangeV1).executeBurnRedeem({
  //   burnItems: [
  //     {
  //       id: "91628078082115406611249698762269260163838683122476090815903466512646985743024",
  //       quantity: 1,
  //       tokenType: 0,
  //       collection: "******************************************",
  //     },
  //   ],
  //   redeemItems: [
  //     {
  //       id: "86958057122118894525889288858420100310895216916339539471994696133372654478124",
  //       quantity: 1,
  //       tokenType: 0,
  //       collection: "******************************************",
  //     },
  //   ],
  //   receiver: "******************************************",
  //   redeemTimes: 1,
  //   totalRedeemTimes: 100,
  //   startAt: 1694492943,
  //   expireAt: 0,
  //   eventId: "0x64ffe93290bd29823afcb2ec",
  //   signature:
  //     "0x1b63e63783d47755b53edbca202b3409cf040a13f717ffebd9bf926200ff950a4cc4b2f626e1480f077d84fd7d6ea63cd9dde1abdd02b8dbb47a78d15c9c31141c",
  // });

  const abi = JSON.parse(
    (await fsPromises.readFile("./artifacts/contracts/src/exchanges/NFTExchangeV1.sol/NFTExchangeV1.json")).toString()
  )["abi"];
  
  console.log(abi);

  const proxy = new ethers.Contract("******************************************", abi, deployer);

  let waiting = await proxy.executeBurnRedeem({
    burnItems: [
      {
        id: "91628078082115406611249698762269260163838683122476090815903466512646985743024",
        quantity: 1,
        tokenType: 0,
        collection: "******************************************",
      },
    ],
    redeemItems: [
      {
        id: "86958057122118894525889288858420100310895216916339539471994696133372654478124",
        quantity: 1,
        tokenType: 0,
        collection: "******************************************",
      },
    ],
    receiver: "******************************************",
    redeemTimes: 1,
    totalRedeemTimes: 100,
    startAt: 1694492943,
    expireAt: 0,
    eventId: "0x64ffe93290bd29823afcb2ec",
    signature:
      "0x1b63e63783d47755b53edbca202b3409cf040a13f717ffebd9bf926200ff950a4cc4b2f626e1480f077d84fd7d6ea63cd9dde1abdd02b8dbb47a78d15c9c31141c",
  });

  await waiting.wait();

  console.log(`tx: ${waiting.hash}`);

  // console.log(hashBurnRedeem(_burnItems, _redeemItems, RECIPIENT, 1, 5, 0, _expireAt, "0xabc123"));

  console.log(
    hashBurnRedeem(
      [
        {
          id: "76922230092905295167948976286653073817408769185613153271066357247105540488968",
          quantity: 1,
          tokenType: 0,
          collection: "******************************************",
        },
        {
          id: "85003561628692812923342894928229009219765207790136449391948836279290031849522",
          quantity: 1,
          tokenType: 0,
          collection: "******************************************",
        },
      ],
      [
        {
          id: "91628078082115406611249698762269260163838683122476090815903466512646985743024",
          quantity: 1,
          tokenType: 0,
          collection: "******************************************",
        },
      ],
      "******************************************",
      1,
      100,
      1694433609,
      0,
      "0x64ff0162105c558c0177e9ed"
    )
  );
}

const hashBurnRedeem = (
  burnItems: any[],
  redeemItems: any[],
  receiver: string,
  redeemTimes: number,
  totalRedeemTimes: number,
  startAt: number,
  expireAt: number,
  eventId: string
): string => {
  let types: string[] = [];
  let values: any[] = [];

  // let data: Mixed[] = [];

  burnItems.map((e) => {
    values = [...values, e.id, e.quantity, e.tokenType, e.collection];
    types = [...types, "uint256", "uint256", "uint256", "address"];

    // data = [
    //   ...data,
    //   { type: "uint256", value: e.id },
    //   { type: "uint256", value: e.quantity },
    //   { type: "uint256", value: e.tokenType },
    //   { type: "address", value: e.collection },
    // ];
  });

  redeemItems.map((e) => {
    values = [...values, e.id, e.quantity, e.tokenType, e.collection];
    types = [...types, "uint256", "uint256", "uint256", "address"];
  });

  values = [...values, receiver, redeemTimes, totalRedeemTimes, startAt, expireAt, eventId];
  types = [...types, "address", "uint256", "uint256", "uint256", "uint256", "bytes"];

  console.log();

  const messageHash = ethers.solidityPackedKeccak256(types, values);
  return messageHash;
};

const checkHash = () => {
  const data = [
    { type: "uint256", value: "89084383204077350415682155609290564629089540362852521884108272654785302398815" },
    { type: "uint256", value: 1 },
    { type: "uint256", value: 0 },
    { type: "address", value: "******************************************" },
    { type: "uint256", value: "93506515050957082985457563480817296288072189985464790799587319231291805444551" },
    { type: "uint256", value: 1 },
    { type: "uint256", value: 0 },
    { type: "address", value: "******************************************" },
    { type: "uint256", value: "23056972257584122588766477860988394848268072558426102459589364895394257498892" },
    { type: "uint256", value: 1 },
    { type: "uint256", value: 0 },
    { type: "address", value: "******************************************" },
    { type: "uint256", value: "51837440282754471388186116281041887784385011894611435106451215454857102489262" },
    { type: "uint256", value: 5 },
    { type: "uint256", value: 1 },
    { type: "address", value: "0x43ad1C277DBCE6b053C00fbdF240163Bf86337A6" },
    { type: "address", value: "******************************************" },
    { type: "uint256", value: 1 },
    { type: "uint256", value: 1 },
    { type: "uint256", value: 1694072653 },
    { type: "uint256", value: 0 },
  ];
  const preHash = "0xed2a5700bd4c763a698db0d2262ef083799cf529b9bec41c23cde5103b1e44c5";
  let types: any[] = [];
  let values: any[] = [];
  data.map((e) => {
    types = [...types, e.type];
    values = [...values, e.value];
  });

  const messageHash = ethers.solidityPackedKeccak256(types, values);
  console.log(messageHash);
  console.log(preHash);
};

// We recommend this pattern to be able to use async/await everywhere
// and properly handle errors.
main().catch((error) => {
  console.error(error);
  process.exitCode = 1;
});
