import { ethers } from "hardhat";
import {
  deployContract,
  deployMultiContract,
  deployContractUpgradeable,
  deployMultiContractUpgradeable,
} from "../utils/contract";
import { getContract, getBlockTimeStamp, createSignature } from "../utils/helper";
import { SAVE_PATH, ADMIN, RECIPIENT, SIGNER, ERC_721, ERC_1155, SUNTORY_SAVE_PATH } from "../utils/constant";
import { NFTExchangeV1, NFTExchangeProxy, BurnRedeemHandler } from "../../typechain-types";
import { ItemsStruct } from "../../typechain-types/contracts/src/exchanges/NFTExchangeV1";
// import { Mixed } from "web3/utils";
import { promises as fsPromises } from "fs";
import { SignerWithAddress } from "@nomicfoundation/hardhat-ethers/signers";
import { getContractFactory } from "@nomicfoundation/hardhat-ethers/types";

const createSig = async (data: any[], types: any[], wallet: any): Promise<String> => {
  const messageHash = ethers.solidityPackedKeccak256(types, data);

  // STEP 2: 32 bytes of data in Uint8Array
  const messageHashBinary = ethers.getBytes(messageHash);

  // STEP 3: To sign the 32 bytes of data, make sure you pass in the data
  const signature = await wallet.signMessage(messageHashBinary);

  return signature;
};

const createSignature_v2 = async (data: any[], types: any[], wallet: any) => {
  // STEP 1:
  // building hash has to come from system address
  // 32 bytes of data
  const hashData = data.reduce(
      (pre, cur, idx) => [
          [...pre[0], types[idx]],
          [...pre[1], cur],
      ],
      [[], []]
  );
  const messageHash = ethers.solidityPackedKeccak256(
      hashData[0],
      hashData[1]
  );

  // STEP 2: 32 bytes of data in Uint8Array
  const messageHashBinary = ethers.getBytes(messageHash);

  // STEP 3: To sign the 32 bytes of data, make sure you pass in the data
  return (await wallet.signMessage(messageHashBinary));
};

async function main() {
  const [deployer] = await ethers.getSigners();
  const signer = new ethers.Wallet("a3911655f6890a4ece9cb18f5c394bff1fd2cf7f10e9b874f434cd5e68a03ffe")
  console.log("signer: ", signer.address);

  let Contract = await ethers.getContractFactory("TestSignatureUtils");

  let contract: any = Contract.attach("******************************************");

  let hash = await contract.hash(
    [
        "0x365766a14f43ef87f6b4a3479397a84ea1768d6b972271e614d297c94c46a248",
        "1",
        "1",
        ethers.parseEther("0.0001").toString(),
        "1",
        "1",
        "1",
        "150",
        "1",
        "******************************************",
        "******************************************",
        ethers.ZeroAddress,
        "******************************************",
        "0x107d0a1620ac32b8b398e93f38f89ea8f8c963228d48d1ec9347b7c6fdfaff4956e20f5fae9e66a502269c73b71ab375c2c525e6d24a4657769a42aaff04fc5e1c",
    ]
  )

  console.log(hash);

  let sig = "0x27bd63e5a931c27edcb273f8a6dbc5e3e3930726a8b75651ce6938bc40ee3c996a71d9f13a5c64379639d487f6a472e892d56e4d93ec389fa259d75cd8f2295c1c"

  console.log(await contract.isValid(hash, signer.address, sig));

  

  
}


// We recommend this pattern to be able to use async/await everywhere
// and properly handle errors.
main().catch((error) => {
  console.error(error);
  process.exitCode = 1;
});
