import { ethers } from "hardhat";
import {
  deployContract,
  deployMultiContract,
  deployContractUpgradeable,
  deployMultiContractUpgradeable,
} from "../utils/contract";
import { getContract, getBlockTimeStamp, createSignature } from "../utils/helper";
import { SAVE_PATH, ADMIN, RECIPIENT, SIGNER, ERC_721, ERC_1155 } from "../utils/constant";
import { NFTExchangeV1, NFTExchangeProxy, BurnRedeemHandler } from "../../typechain-types";
import { ItemsStruct } from "../../typechain-types/contracts/src/exchanges/NFTExchangeV1";
// import { Mixed } from "web3/utils";
import { promises as fsPromises } from "fs";

async function main() {
  const [deployer] = await ethers.getSigners();
  console.log("signer: ", deployer.address);

  const _NFTExchangeProxy = await getContract("NFTExchangeProxy", SAVE_PATH);

  let _NFTExchangeV1 = await getContract("NFTExchangeV1", SAVE_PATH);

  _NFTExchangeV1 = _NFTExchangeV1.attach(_NFTExchangeProxy.target);

  let w = await (_NFTExchangeV1 as NFTExchangeV1).setSigner(deployer.address, true);
  await w.wait();
  console.log(`signer tx: ${w.hash}`);

  const signature = await signBurnRedeem([10, 0, 0], [deployer.address], [], ["", "0x64ffe93290bd29823a"]);
  console.log(`signature: ${signature}`);

  process.exit();

  let waiting = await (_NFTExchangeV1 as NFTExchangeV1).cancelBurnRedeemEvent(
    [10, 0, 0],
    [deployer.address],
    [],
    [signature, "0x64ffe93290bd29823a"]
  );
  await waiting.wait();
  console.log(`tx: ${waiting.hash}`);
}

const signBurnRedeem = async (data: any[], addrs: any[], strs: any[], sig: any[]): Promise<string> => {
  let values: string[] = [sig[1], data[0], addrs[0], data[1], data[2]];
  let types: any[] = ["bytes", "uint256", "address", "uint256", "uint256"];

  //   abi.encodePacked(
  //     burnRedeem.eventId,
  //     burnRedeem.totalRedeemTimes,
  //     burnRedeem.creator,
  //     burnRedeem.start,
  //     burnRedeem.end
  // )
  // /**
  //      * @dev Cancel burn redeem event
  //      * @param data [0] totalRedeemTimes [1] start, [2] end
  //      * @param addr [0] creator
  //      * @param _strs []
  //      * @param signatures [0] cancelBurnRedeemSignature [1] eventId
  //      */

  const messageHash = ethers.solidityPackedKeccak256(types, values);

  // STEP 2: 32 bytes of data in Uint8Array
  const messageHashBinary = ethers.getBytes(messageHash);

  // STEP 3: To sign the 32 bytes of data, make sure you pass in the data
  const [deployer] = await ethers.getSigners();
  const signature = await deployer.signMessage(messageHashBinary);

  return signature;
};

// We recommend this pattern to be able to use async/await everywhere
// and properly handle errors.
main().catch((error) => {
  console.error(error);
  process.exitCode = 1;
});
