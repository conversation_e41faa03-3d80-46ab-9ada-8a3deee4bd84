import { ethers } from "hardhat";
import { createSignature } from "../utils/helper";
import dotenv from "dotenv";
dotenv.config();

async function main() {
  const [deployer] = await ethers.getSigners();
  console.log("deployer: ", deployer.address);

  // Properly typed arrays for buyNowNative function
  // Data array: [0] tokenID, [1] quantity, [2] sellOrderSupply, [3] sellOrderPrice, [4] enableMakeOffer
  //             [5] buyingAmount, [6] tokenType, [7] partnerType, [8] partnerFee, [9] transactionType,
  //             [10] storeFeeRatio, [11] shippingFee, [12] expiredAt, [13-...] payoutRatios
  // const dataArray: number[] = [
  //   4, 150, 100, 2500, 1, 1, 0, 1, 0, 0, 0, 189017, 1753358536, 10000
  // ];

  //   [
  //     "1",
  //     "100",
  //     "99",
  //     "20000",
  //     "1",
  //     1,
  //     0,
  //     1,
  //     "0",
  //     0,
  //     0,
  //     "189037",
  //     10000
  // ]
  const dataArray: number[] = [
    4,
    150,
    100,
    2500,
    1,
    1,
    0,
    1,
    0,
    0,
    0,
    189035,
    1753329309,
    10000
];

  // Address array: [0] creator, [1] tokenAddress, [2] collectionAddress, [3] signer, [4] storeAddress,
  //                [5] receiver, [6-...] payoutAddress
  const addressArray: string[] = [
    "0x06DB1eeb30cB6A1B19f0e987560EBaff6E1477f9",
    "0x5425890298aed601595a70AB815c96711a31Bc65",
    "0x3E963087a91dB1d7D19E48154D734394Ee4f17EE",
    "0xE7436132C68625207aBB9fEe888d4ee4eae2b6e3",
    "0x06DB1eeb30cB6A1B19f0e987560EBaff6E1477f9",
    "0xCcA55A052F2140541b6650093890A0a21405dCc7",
    "0x06DB1eeb30cB6A1B19f0e987560EBaff6E1477f9"
];
  const stringArray: string[] = [
    "6881ad5319580fb15f4977e8"
];
  const bytesArray: string[] = [
    "0x95308cc4f3f5f01fd874ffa9ed3d46e4df152b925f33141c2a24eca02880d55817a8e0dd02bde3c7ae8007efef0604ba03c1c792c9c46d063b0c5f52af31e1f71b",
    "0x01758888bf015d970e91a121ffb7a2da02a87768daeb14507a96fa4eabe6726a6b0bd3a7f8358a1ee884c360fa18e11465ca809376bb8b09ad898a6af88a1ad01c",
    "0x39e21907132abcb8b1a9fecefbfe49aeff1b35b505ec1647bafbe8aa6bfb8b64469e703d2dc6d046bcbb1dc45a5f71f00204978c21012e432152cc81a95e52251b"
];

  // Create a signer for signing the request
  const signer = new ethers.Wallet(process.env.TEST_KEY as string);
  console.log("signer: ", signer.address);

    // Create signature that matches the hash function in BuyHandler.sol
    // The hash function expects: tokenID, quantity, sellOrderSupply, sellOrderPrice, enableMakeOffer,
    // tokenType, partnerType, partnerFee, transactionType, shippingFee, expiredAt,
    // creator, receiver, tokenAddress, contractAddress, sellOrderSignature
  
  let signature = await createSignature(
    [
      dataArray[0].toString(), // tokenID
      dataArray[1].toString(), // quantity
      dataArray[2].toString(), // sellOrderSupply
      dataArray[3].toString(), // sellOrderPrice
      dataArray[4].toString(), // enableMakeOffer
      dataArray[6].toString(), // tokenType
      dataArray[7].toString(), // partnerType
      dataArray[8].toString(), // partnerFee
      dataArray[9].toString(), // transactionType
      dataArray[11].toString(), // shippingFee
      dataArray[12].toString(), // expiredAt
      addressArray[0], // creator
      addressArray[5], // receiver
      addressArray[1], // tokenAddress
      addressArray[2], // contractAddress
      bytesArray[1], // sellOrderSignature
    ],
    [
      "uint256",
      "uint256",
      "uint256",
      "uint256",
      "uint256", // tokenID, quantity, sellOrderSupply, sellOrderPrice, enableMakeOffer
      "uint256",
      "uint256",
      "uint256",
      "uint256", // tokenType, partnerType, partnerFee, transactionType
      "uint256",
      "uint256", // shippingFee, expiredAt
      "address",
      "address",
      "address",
      "address", // creator, receiver, tokenAddress, contractAddress
      "bytes", // sellOrderSignature
    ],
    signer
  );
  console.log("Signature: ", signature);

  // Update the bytesArray with the generated signature
  bytesArray[0] = signature;

  let _NFTExchangeV1 = await ethers.getContractAt(
    "NFTExchangeV1",
    "******************************************"
  );

  let tx = await _NFTExchangeV1.buyNowNative(
    dataArray,
    addressArray,
    stringArray,
    bytesArray
  );
  let receipt = await tx.wait();

  console.log(receipt?.hash);

  //   console.log(`Token fee: ${waiting.toString()}`);
}

// We recommend this pattern to be able to use async/await everywhere
// and properly handle errors.
main().catch((error) => {
  console.error(error);
  process.exitCode = 1;
});
