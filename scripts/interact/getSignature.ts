import { ethers } from "hardhat";
import {
  deployContract,
  deployMultiContract,
  deployContractUpgradeable,
  deployMultiContractUpgradeable,
} from "../utils/contract";
import { getContract, getBlockTimeStamp, createSignature } from "../utils/helper";
import { SAVE_PATH, ADMIN, RECIPIENT, SIGNER, ERC_721, ERC_1155, SUNTORY_SAVE_PATH } from "../utils/constant";
import { NFTExchangeV1, NFTExchangeProxy, BurnRedeemHandler } from "../../typechain-types";
import { ItemsStruct } from "../../typechain-types/contracts/src/exchanges/NFTExchangeV1";
// import { Mixed } from "web3/utils";
import { promises as fsPromises } from "fs";
import { SignerWithAddress } from "@nomicfoundation/hardhat-ethers/signers";

const createSig = async (data: any[], types: any[], wallet: any): Promise<String> => {
  const messageHash = ethers.solidityPackedKeccak256(types, data);

  // STEP 2: 32 bytes of data in Uint8Array
  const messageHashBinary = ethers.getBytes(messageHash);

  // STEP 3: To sign the 32 bytes of data, make sure you pass in the data
  const signature = await wallet.signMessage(messageHashBinary);

  return signature;
};

const createSignature_v2 = async (data: any[], types: any[], wallet: any) => {
  // STEP 1:
  // building hash has to come from system address
  // 32 bytes of data
  const hashData = data.reduce(
      (pre, cur, idx) => [
          [...pre[0], types[idx]],
          [...pre[1], cur],
      ],
      [[], []]
  );
  const messageHash = ethers.solidityPackedKeccak256(
      hashData[0],
      hashData[1]
  );

  // STEP 2: 32 bytes of data in Uint8Array
  const messageHashBinary = ethers.getBytes(messageHash);

  // STEP 3: To sign the 32 bytes of data, make sure you pass in the data
  return (await wallet.signMessage(messageHashBinary));
};

async function main() {
  const [deployer] = await ethers.getSigners();

  const signer = new ethers.Wallet("a3911655f6890a4ece9cb18f5c394bff1fd2cf7f10e9b874f434cd5e68a03ffe")
  console.log("signer: ", signer.address);

  const totalPrice = "0.0001"
  const nftBuyRequestSignture = await createSig(
    [
        "4",
        "1",
        "100",
        ethers.parseEther(totalPrice).toString(),
        "1",
        "1",
        "0",
        "200",
        "0",
        "******************************************",
        "******************************************",
        ethers.ZeroAddress,
        "******************************************",
        "0x",
    ],
    [
        "uint256",
        "uint256",
        "uint256",
        "uint256",
        "uint256",
        "uint256",
        "uint256",
        "uint256",
        "uint256",
        "address",
        "address",
        "address",
        "address",
        "bytes"
    ],
    signer
  )

  console.log(nftBuyRequestSignture);
}

const signBurnRedeem = async (data: any[], addrs: any[], strs: any[], sig: any[]): Promise<string> => {
  let values: string[] = [sig[1], data[0], addrs[0], data[1], data[2]];
  let types: any[] = ["bytes", "uint256", "address", "uint256", "uint256"];

  //   abi.encodePacked(
  //     burnRedeem.eventId,
  //     burnRedeem.totalRedeemTimes,
  //     burnRedeem.creator,
  //     burnRedeem.start,
  //     burnRedeem.end
  // )
  // /**
  //      * @dev Cancel burn redeem event
  //      * @param data [0] totalRedeemTimes [1] start, [2] end
  //      * @param addr [0] creator
  //      * @param _strs []
  //      * @param signatures [0] cancelBurnRedeemSignature [1] eventId
  //      */

  const messageHash = ethers.solidityPackedKeccak256(types, values);

  // STEP 2: 32 bytes of data in Uint8Array
  const messageHashBinary = ethers.getBytes(messageHash);

  // STEP 3: To sign the 32 bytes of data, make sure you pass in the data
  const [deployer] = await ethers.getSigners();
  const signature = await deployer.signMessage(messageHashBinary);

  return signature;
};

// We recommend this pattern to be able to use async/await everywhere
// and properly handle errors.
main().catch((error) => {
  console.error(error);
  process.exitCode = 1;
});
