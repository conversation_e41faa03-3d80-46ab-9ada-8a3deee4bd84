import { ethers } from "hardhat";
import {
  deployContract,
  deployMultiContract,
  deployContractUpgradeable,
  deployMultiContractUpgradeable,
} from "../utils/contract";
import { getContract, getBlockTimeStamp, createSignature } from "../utils/helper";
import { SAVE_PATH, ADMIN, RECIPIENT, SIGNER, ERC_721, ERC_1155 } from "../utils/constant";
import { NFTExchangeV1, NFTExchangeProxy, BurnRedeemHandler, NFTify1155, NFTify721 } from "../../typechain-types";
import { ItemsStruct, BurnRedeemComponentStruct } from "../../typechain-types/contracts/src/exchanges/NFTExchangeV1";
import { BaseContract, BigNumberish } from "ethers";
import Web3 from "web3";
// import { Mixed } from "web3/utils";

async function main() {
  const [deployer] = await ethers.getSigners();
  // console.log("signer: ", deployer.address);

  const _NFTify721_test = (await ethers.getContractAt(
    "contracts/collections/NFTify721.sol:NFTify721",
    "******************************************"
  ) as BaseContract) as NFTify721;
  const _NFTify1155_test = (await ethers.getContractAt(
    "contracts/collections/NFTify1155.sol:NFTify1155",
    "******************************************"
  ) as BaseContract) as NFTify1155;

  let waiting;
  // waiting = await _NFTify1155_test.mint(RECIPIENT, 1, 2, "0x");
  // await waiting.wait();
  // console.log(`mint 1155: ${waiting.hash}`);

  // waiting = await _NFTify721_test.mint(RECIPIENT, 0, "0x");
  // await waiting.wait();
  // console.log(`mint 721: ${waiting.hash}`)

  let _NFTExchangeV1 = await getContract("NFTExchangeV1", SAVE_PATH) as NFTExchangeV1;

  _NFTExchangeV1 = _NFTExchangeV1.attach("******************************************") as NFTExchangeV1;

  await _NFTExchangeV1.setSigner(SIGNER, true);

  // await _NFTExchangeV1.signer;

  const _expireAt = await getBlockTimeStamp();

  const _burnItems = [
    {
      id: 0,
      quantity: 1,
      tokenType: ERC_721,
      collection: _NFTify721_test.target,
    },
    {
      id: 1,
      quantity: 2,
      tokenType: ERC_1155,
      collection: _NFTify1155_test.target,
    },
  ];
  const _redeemItems = [
    {
      id: 10,
      quantity: 2,
      tokenType: ERC_1155,
      collection: _NFTify1155_test.target,
    },
  ];

  const _signature = await hashBurnRedeem(_burnItems, _redeemItems, RECIPIENT, 1, 5, 0, _expireAt, "0xabc213");

  const _burnRedeemParam: BurnRedeemComponentStruct = {
    burnItems: _burnItems,
    redeemItems: _redeemItems,
    receiver: RECIPIENT,
    redeemTimes: 1,
    totalRedeemTimes: 5,
    startAt: 0,
    expireAt: _expireAt,
    eventId: "0xabc213",
    signature: _signature,
  };

  const _param = await convertType();
  // console.log(`now: ${await getBlockTimeStamp()}`)

  const _BurnRedeemHandler = (await getContract("BurnRedeemHandler", SAVE_PATH)) as BurnRedeemHandler;

  waiting = await _NFTExchangeV1.executeBurnRedeem(_param);
  await waiting.wait();
  console.log(`burn redeem: ${waiting.hash}`);

  console.log(hashBurnRedeem(_burnItems, _redeemItems, RECIPIENT, 1, 5, 0, _expireAt, "0xabc213"));
}

const hashBurnRedeem = async (
  burnItems: ItemsStruct[],
  redeemItems: ItemsStruct[],
  receiver: string,
  redeemTimes: BigNumberish,
  totalRedeemTimes: BigNumberish,
  startAt: BigNumberish,
  expireAt: BigNumberish,
  eventId: string
): Promise<string> => {
  let types: string[] = [];
  let values: any[] = [];

  burnItems.map((e) => {
    values = [...values, e.id, e.quantity, e.tokenType, e.collection];
    types = [...types, "uint256", "uint256", "uint256", "address"];
  });

  redeemItems.map((e) => {
    values = [...values, e.id, e.quantity, e.tokenType, e.collection];
    types = [...types, "uint256", "uint256", "uint256", "address"];
  });

  values = [...values, receiver, redeemTimes, totalRedeemTimes, startAt, expireAt, eventId];
  types = [...types, "address", "uint256", "uint256", "uint256", "uint256", "bytes"];

  const messageHash = ethers.solidityPackedKeccak256(types, values);

  // // STEP 2: 32 bytes of data in Uint8Array
  // const messageHashBinary = ethers.utils.arrayify(messageHash);

  // // STEP 3: To sign the 32 bytes of data, make sure you pass in the data
  // const [deployer] = await ethers.getSigners();
  // const signature = await deployer.signMessage(messageHashBinary);

  // // Recover the public key
  // const publicKey = ethers.utils.recoverPublicKey(
  //   messageHash,
  //   signature
  // );

  // const signingAddress = ethers.utils.computeAddress(publicKey);

  // console.log(`signing addr: ${signingAddress}, signer: ${deployer.address}`);

  // process.exit();

  // return signature;

  return messageHash;
};

const convertType = async (): Promise<BurnRedeemComponentStruct> => {
  // const data = [
  //   [
  //     "76922230092905295167948976286653073817408769185613153271066357247105540488968",
  //     1,
  //     0,
  //     "******************************************",
  //     "85003561628692812923342894928229009219765207790136449391948836279290031849522",
  //     1,
  //     0,
  //     "******************************************",
  //   ],
  //   [
  //     "61226174661987275394454596084365052790596376954122414812704399727071986628078",
  //     1,
  //     0,
  //     "******************************************",
  //   ],
  //   "******************************************",
  //   1,
  //   100,
  //   // 1694431644
  //   // 1694426050
  //   1694426050,
  //   0,
  //   "0x64fee3d789b55c708fc14af0",
  //   "0x373d142f465b76ea667dd594fffddee93553427ee4eaf332a2e1d1022f136be05b91bf5b914fb84eb25a752be57743b45cd30052a71d9ae9880dc7b69edb558e1c",
  // ];

  // [
  //   { type: "uint256", value: "89084383204077350415682155609290564629089540362852521884108272654785302398815" },
  //   { type: "uint256", value: 1 },
  //   { type: "uint256", value: 0 },
  //   { type: "address", value: "******************************************" },
  //   { type: "uint256", value: "93506515050957082985457563480817296288072189985464790799587319231291805444551" },
  //   { type: "uint256", value: 1 },
  //   { type: "uint256", value: 0 },
  //   { type: "address", value: "******************************************" },
  //   { type: "uint256", value: "23056972257584122588766477860988394848268072558426102459589364895394257498892" },
  //   { type: "uint256", value: 1 },
  //   { type: "uint256", value: 0 },
  //   { type: "address", value: "******************************************" },
  //   { type: "uint256", value: "51837440282754471388186116281041887784385011894611435106451215454857102489262" },
  //   { type: "uint256", value: 5 },
  //   { type: "uint256", value: 1 },
  //   { type: "address", value: "0x43ad1C277DBCE6b053C00fbdF240163Bf86337A6" },
  //   { type: "address", value: "0xb2670863B5ca94caBe5792F80aB2A8F76bE9A5F4" },
  //   { type: "uint256", value: 1 },
  //   { type: "uint256", value: 1 },
  //   { type: "uint256", value: 1694072653 },
  //   { type: "uint256", value: 0 },
  //   { type: "string", value: "0x64fec178dcc06e27ecb0cd87" },
  // ];

  // [
  //   { type: "uint256", value: "76922230092905295167948976286653073817408769185613153271066357247105540488968" },
  //   { type: "uint256", value: 1 },
  //   { type: "uint256", value: 0 },
  //   { type: "address", value: "******************************************" },
  //   { type: "uint256", value: "85003561628692812923342894928229009219765207790136449391948836279290031849522" },
  //   { type: "uint256", value: 1 },
  //   { type: "uint256", value: 0 },
  //   { type: "address", value: "******************************************" },
  //   { type: "uint256", value: "91628078082115406611249698762269260163838683122476090815903466512646985743024" },
  //   { type: "uint256", value: 1 },
  //   { type: "uint256", value: 0 },
  //   { type: "address", value: "******************************************" },
  //   { type: "address", value: "******************************************" },
  //   { type: "uint256", value: 1 },
  //   { type: "uint256", value: 100 },
  //   { type: "uint256", value: 1694433609 },
  //   { type: "uint256", value: 0 },
  //   { type: "bytes", value: "0x64ff0162105c558c0177e9ed" },
  // ];

  const _burnItems = [
    {
      id: "76922230092905295167948976286653073817408769185613153271066357247105540488968",
      quantity: 1,
      tokenType: 0,
      collection: "******************************************",
    },
    {
      id: "85003561628692812923342894928229009219765207790136449391948836279290031849522",
      quantity: 1,
      tokenType: 0,
      collection: "******************************************",
    },
  ];
  const _redeemItems = [
    {
      id: "91628078082115406611249698762269260163838683122476090815903466512646985743024",
      quantity: 1,
      tokenType: 0,
      collection: "******************************************",
    },
  ];

  const _burnRedeemParam: BurnRedeemComponentStruct = {
    burnItems: _burnItems,
    redeemItems: _redeemItems,
    receiver: "0xb2670863B5ca94caBe5792F80aB2A8F76bE9A5F4",
    redeemTimes: 1,
    totalRedeemTimes: 1,
    startAt: 1694072653,
    expireAt: 0,
    eventId: "0x64fec178dcc06e27ecb0cd87",
    signature:
      "0x596ac389333cfb987b0f8eb436ec1c318f337383571a7eb0390f353058d5bd5439064da5b491217440cc254efad85361ab69819e867ab76545976c2e398322211c",
  };

  const hash = await hashBurnRedeem(
    _burnItems,
    _redeemItems,
    "******************************************",
    "1",
    "100",
    "1694433609",
    "0",
    "0x64ff0162105c558c0177e9ed"
  );

  // console.log("0xd3a4c22235a94f0b9c2ab3080c2ce03573a5a8089eacd7009f599e06cbc5f440");
  console.log(hash);

  // // Recover the public key
  // const publicKey = ethers.utils.recoverPublicKey(
  //   hash,
  //   "0xfef097df8391f15f9ac79f4f2367c1178d5129616ae1df244931c4a9f743b6eb59aafd6228a52242f72507c83cc0b59e2c48ff328e8571b8f2a3fdc789b624aa1b"
  // );
  // const signingAddress = ethers.utils.computeAddress(publicKey);

  // console.log(`signing addr: ${signingAddress}`);

  return _burnRedeemParam;
};

// We recommend this pattern to be able to use async/await everywhere
// and properly handle errors.
main().catch((error) => {
  console.error(error);
  process.exitCode = 1;
});
