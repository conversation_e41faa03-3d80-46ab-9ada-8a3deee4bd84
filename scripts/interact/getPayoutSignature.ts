// import { ethers } from "hardhat";
// import { createSignature } from "../utils/helper";
// import dotenv from "dotenv";
// dotenv.config();

// async function main() {
//   const [deployer] = await ethers.getSigners();
//   console.log("deployer: ", deployer.address);

//   // Properly typed arrays for buyNowNative function
//   const dataArray: number[] = [
//     4, 150, 100, 2500, 1, 1, 0, 1, 0, 0, 0, 189035, 1753358536,
//   ];
//   const addressArray: string[] = [
//     "******************************************",
//     "******************************************",
//     "******************************************",
//     "******************************************",
//     "******************************************",
//     "******************************************",
//     "******************************************",
//   ];
//   const stringArray: string[] = ["68808f2b814c3d4c65af4cc8"];
//   const bytesArray: string[] = [
//     "0x",
//     "0x01758888bf015d970e91a121ffb7a2da02a87768daeb14507a96fa4eabe6726a6b0bd3a7f8358a1ee884c360fa18e11465ca809376bb8b09ad898a6af88a1ad01c",
//     "0x39e21907132abcb8b1a9fecefbfe49aeff1b35b505ec1647bafbe8aa6bfb8b64469e703d2dc6d046bcbb1dc45a5f71f00204978c21012e432152cc81a95e52251b",
//   ];

//   // Create a signer for signing the request
//   const signer = new ethers.Wallet(process.env.DEV_KEY as string);
//   console.log("signer: ", signer.address);

//   const FeeUtils = await ethers.getContractFactory("NFTFeeUtils");
//   const feeUtils = await FeeUtils.deploy();
//   await feeUtils.waitForDeployment();

//   let payoutGroup = {
//     storeFeeRatio: dataArray[12],
//     storeAddress: addressArray[5],
//     artistAddress: addressArray[0],
//     payoutAddress: addressArray.slice(6),
//     payoutRatios: dataArray.slice(13),
//   }

//   const expectedHash = await feeUtils.hash(payoutGroup);
//   console.log("Expected hash: ", expectedHash);

//   // Use the Ethereum signed message hash for signing (to match the contract's expectation)
//   // const ethSignedMessageHash = ethers.hashMessage(ethers.getBytes(expectedHash));
//   const signature = await signer.signMessage(ethers.getBytes(expectedHash));
//   console.log("Signature: ", signature);

//   // The contract's recoverSigner expects the hash with Ethereum signed message prefix
//   const result = await feeUtils.recoverSigner(expectedHash, signature);
//   console.log("Result: ", result);
// }

// // We recommend this pattern to be able to use async/await everywhere
// // and properly handle errors.
// main().catch((error) => {
//   console.error(error);
//   process.exitCode = 1;
// });
