import { ethers } from "hardhat";
import {
  deployContract,
  deployMultiContract,
  deployContractUpgradeable,
  deployMultiContractUpgradeable,
} from "../utils/contract";
import { getContract, getBlockTimeStamp, createSignature } from "../utils/helper";
import { SAVE_PATH, ADMIN, REC<PERSON>IENT, SIGNER, ERC_721, ERC_1155 } from "../utils/constant";
import { NFTExchangeV1, NFTExchangeProxy, BurnRedeemHandler } from "../../typechain-types";
import { ItemsStruct } from "../../typechain-types/contracts/src/exchanges/NFTExchangeV1";
// import { Mixed } from "web3/utils";
import { promises as fsPromises } from "fs";
import { getContractFactory } from "@nomicfoundation/hardhat-ethers/types";

async function main() {
  const [deployer] = await ethers.getSigners();
  console.log("signer: ", deployer.address);

  let collection_address = "******************************************" // 721
  // let collection_address = "******************************************" //1155
  let baseUri = ""

  const Collection = await ethers.getContractFactory("NFTify721");
  // const Collection = await ethers.getContractFactory("contracts/collections/NFTify1155.sol:NFTify1155");

  let collection: any = Collection.attach(collection_address)

  let w = await collection.setBaseURI(baseUri); //721
  // let w = await collection.setURI(baseUri) // 1155
  await w.wait();
  console.log(` tx: ${w.hash}`);
}

// We recommend this pattern to be able to use async/await everywhere
// and properly handle errors.
main().catch((error) => {
  console.error(error);
  process.exitCode = 1;
});
