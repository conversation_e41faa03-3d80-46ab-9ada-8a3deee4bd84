import { ethers } from "hardhat";
import {
  deployContract,
  deployMultiContract,
  deployContractUpgradeable,
  deployMultiContractUpgradeable,
} from "../utils/contract";
import { SAVE_PATH, ADMIN, RECIPIENT, SIGNER } from "../utils/constant";
import { NFTExchangeV1, NFTExchangeProxy } from "../../typechain-types";

async function main() {
  let _NFTExchangeV1 = await ethers.getContractAt("NFTExchangeV1", "******************************************");

  let BuyHandler = await ethers.getContractFactory("NFTFeeUtils");
  let buyHandler = await BuyHandler.deploy();
  await buyHandler.waitForDeployment();

  console.log("Buy Handler: ", await buyHandler.getAddress());

  let waiting = await _NFTExchangeV1.setFeeUtils(await buyHandler.getAddress());
  await waiting.wait();
  console.log(`Buy Handler: ${await _NFTExchangeV1.feeUtils()}`);
}

// We recommend this pattern to be able to use async/await everywhere
// and properly handle errors.
main().catch((error) => {
  console.error(error);
  process.exitCode = 1;
});
