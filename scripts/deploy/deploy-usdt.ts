import { ethers, run } from "hardhat";
async function main() {
  const chainId = await ethers.provider
    .getNetwork()
    .then(({ chainId }) => chainId);

  // Get the contract factory
  const ERC20 = await ethers.getContractFactory("Tether");

  // Deploy the contract
  const erc20 = await ERC20.deploy();

  // Wait for the deployment to be mined
  await erc20.waitForDeployment();

  console.log(`ERC20 deployed to: ${await erc20.getAddress()}`);

  await run("verify:verify", {
    address: await erc20.getAddress(),
    constructorArguments: [],
    contract: "contracts/tokens/USDT.sol:Tether"
  });       
}

// Run the main function
main().catch((error) => {
  console.error(error);
  process.exitCode = 1;
});
