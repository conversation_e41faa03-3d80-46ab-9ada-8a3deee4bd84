import { ethers } from "hardhat";
import {
  deployContract,
  deployMultiContract,
  deployContractUpgradeable,
  deployMultiContractUpgradeable,
} from "../utils/contract";
import { getContract, getBlockTimeStamp, createSignature } from "../utils/helper";
import { SAVE_PATH, ADMIN, REC<PERSON>IENT, SIGNER, ERC_721, ERC_1155, SUNTORY_SAVE_PATH } from "../utils/constant";
import { NFTExchangeV1, NFTExchangeProxy } from "../../typechain-types";

async function main() {
  const [deployer] = await ethers.getSigners();
  console.log("signer: ", deployer.address);

  // let [_NFTExchangeV1] = await deployMultiContract(
  //   [
  //     {
  //       name: "NFTExchangeV1",
  //       args: [],
  //     },
  //   ],
  //   SUNTORY_SAVE_PATH,
  //   false
  // );

  // const _NFTExchangeProxy = await ethers.getContractAt("NFTExchangeProxy", "******************************************") as NFTExchangeProxy;

  const Implementation = await ethers.getContractFactory("NFTExchangeV1");
  const implementation = await Implementation.deploy();
  await implementation.waitForDeployment();

  const proxy = await ethers.getContractAt("NFTExchangeProxy", "******************************************") as NFTExchangeProxy;

  // const FeeUtils = await ethers.getContractFactory("NFTFeeUtils");
  // const feeUtils = await FeeUtils.deploy();
  // await feeUtils.waitForDeployment();

  // await proxy.upgradeTo(implementation.target);
  // console.log("upgrade proxy DONE: ", await proxy.implementation());

  
  // let waiting = await _NFTExchangeProxy.upgradeTo(await implementation.getAddress());
  // await waiting.wait();
  // console.log("upgrade proxy DONE: ", waiting.hash, await _NFTExchangeProxy.implementation());

  // let waiting1 = await (_NFTExchangeV1 as NFTExchangeV1).setAdminList(deployer.address, true);
  // await waiting1.wait();

  // waiting = await (_NFTExchangeV1 as NFTExchangeV1).setCancelHandler(_CancelHandler.target);
  // await waiting.wait();
  // console.log("upgrade _CancelHandler DONE");
}

// We recommend this pattern to be able to use async/await everywhere
// and properly handle errors.
main().catch((error) => {
  console.error(error);
  process.exitCode = 1;
});
