import { ethers } from "hardhat";
import {
  deployContract,
  deployMultiContract,
  deployContractUpgradeable,
  deployMultiContractUpgradeable,
} from "../utils/contract";
import { SAVE_PATH, ADMIN, RECIPIENT, SIGNER } from "../utils/constant";
import { NFTExchangeV1, NFTExchangeProxy } from "../../typechain-types";

async function main() {
  // let _NFTExchangeProxy = await ethers.getContractAt("NFTExchangeProxy", "******************************************");

  // let waiting = await (_NFTExchangeProxy as NFTExchangeProxy).setAdminList(ADMIN, true);
  // await waiting.wait();
  // console.log("Set ADMIN list - DONE");

  let _NFTExchangeV1 = await ethers.getContractAt("NFTExchangeV1", "******************************************");

  // const _NFTExchangeV1Proxy = _NFTExchangeV1.attach(_NFTExchangeProxy.target) as NFTExchangeV1;

  // let waiting = await _NFTExchangeV1.setAdminList(ADMIN, true);
  // await waiting.wait();
  // console.log("Set ADMIN list - DONE");

  let waiting = await _NFTExchangeV1.setSigner("******************************************", true);
  await waiting.wait();
  console.log("Set signer - DONE");

  // waiting = await _NFTExchangeV1.setRecipient(RECIPIENT);
  // await waiting.wait();
  // console.log("Set recipient - DONE");
}

// We recommend this pattern to be able to use async/await everywhere
// and properly handle errors.
main().catch((error) => {
  console.error(error);
  process.exitCode = 1;
});
