import { ethers } from "hardhat";
import {
  deployContract,
  deployMultiContract,
  deployContractUpgradeable,
  deployMultiContractUpgradeable,
} from "../utils/contract";
import { getContract, getBlockTimeStamp, createSignature } from "../utils/helper";
import { SAVE_PATH, ADMIN, REC<PERSON>IENT, SIGNER, ERC_721, ERC_1155, SUNTORY_SAVE_PATH } from "../utils/constant";
import { NFTExchangeV1, NFTExchangeProxy, Burn<PERSON><PERSON>emHandler } from "../../typechain-types";
import { ItemsStruct, BurnRedeemComponentStruct } from "../../typechain-types/contracts/src/exchanges/NFTExchangeV1";

async function main() {
  const [deployer] = await ethers.getSigners();
  console.log("deployer: ", deployer.address);

  await deployMultiContract(
    [
      {
        artifactPath: "contracts/collections/NFTify721.sol:NFTify721",
        name: "NFTify721",
        args: ["NFTify721", "N721", "", "******************************************"],
      },
      {
        artifactPath: "contracts/collections/NFTify1155.sol:NFTify1155",
        name: "NFTify1155",
        args: ["NFTify1155", "N1155", "", "******************************************"],
      },
      // {
      //   artifactPath: "contracts/collections/ERC20Permit.sol:NFTify20",
      //   name: "N1",
      //   args: ["NFTify20", "N1"],
      // },
      // {
      //     // artifactPath: "contracts/collections/NFTify1155.sol:NFTify1155",
      //     name: "Tether",
      //     args: [],
      // },
      // {
      //   name: "TestCrossMint",
      //   args: ["******************************************"]
      // }
    ],
    // SUNTORY_SAVE_PATH,
    undefined,
    false
  );
}

// We recommend this pattern to be able to use async/await everywhere
// and properly handle errors.
main().catch((error) => {
  console.error(error);
  process.exitCode = 1;
});
