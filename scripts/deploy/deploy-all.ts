import { ethers } from "hardhat";
import {
  deployContract,
  deployMultiContract,
  deployContractUpgradeable,
  deployMultiContractUpgradeable,
} from "../utils/contract";
import { SAVE_PATH, ADMIN, RECIPIENT, SIGNER, SUNTORY_SAVE_PATH } from "../utils/constant";
import { NFTExchangeV1, NFTExchangeProxy } from "../../typechain-types";

async function main() {
  const [
    _NFTExchangeV1,
    _NFTExchangeProxy,
    _<PERSON><PERSON><PERSON><PERSON>,
    _<PERSON><PERSON><PERSON><PERSON><PERSON>,
    _<PERSON>er<PERSON><PERSON><PERSON>,
    _<PERSON><PERSON><PERSON><PERSON><PERSON>,
    _<PERSON><PERSON><PERSON><PERSON><PERSON>,
    _AirdropHandler,
    _SubscribeHandler,
    _OpenBox,
    _BurnRedeemHandler,
  ] = await deployMultiContract(
    [
      {
        name: "NFTExchangeV1",
        args: [],
      },
      {
        name: "NFTExchangeProxy",
        args: [],
      },
      {
        name: "NFT<PERSON>uyH<PERSON><PERSON>",
        args: [],
      },
      {
        name: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
        args: [],
      },
      {
        name: "N<PERSON>OfferHand<PERSON>",
        args: [],
      },
      {
        name: "NFTCancelHandler",
        args: [],
      },
      {
        name: "NFTFeeUtils",
        args: [],
      },
      {
        name: "NFTAirdropHandler",
        args: [],
      },
      {
        name: "NFTSubscribeHandler",
        args: [],
      },
      {
        name: "NFTOpenBox",
        args: [],
      },
      {
        name: "NFTBurnRedeemHandler",
        args: [],
      }
    ],
    SUNTORY_SAVE_PATH,
    false
  );

  const _MetaHandler = await deployContract(
    {
      name: "NFTMetaHandler",
      args: [_NFTExchangeProxy.target],
    },
    SUNTORY_SAVE_PATH,
    false
  );

  const [signer] = await ethers.getSigners();

  let waiting = await (_NFTExchangeProxy as NFTExchangeProxy).upgradeTo(_NFTExchangeV1.target);
  await waiting.wait();
  console.log(`Implementation: ${await (_NFTExchangeProxy as NFTExchangeProxy).implementation()}`);

  waiting = await (_NFTExchangeProxy as NFTExchangeProxy).setAdminList(signer.address, true);
  await waiting.wait();

  waiting = await (_NFTExchangeProxy as NFTExchangeProxy).setAdminList(ADMIN, true);
  await waiting.wait();
  console.log("Set ADMIN list - DONE");

  const _NFTExchangeV1Proxy = _NFTExchangeV1.attach(_NFTExchangeProxy.target) as NFTExchangeV1;

  waiting = await _NFTExchangeV1Proxy.setRecipient(RECIPIENT);
  await waiting.wait();
  console.log(`Recipient: ${await _NFTExchangeV1Proxy.recipient()}`);

  waiting = await _NFTExchangeV1Proxy.setSigner(SIGNER, true);
  await waiting.wait();
  console.log("Set signer - DONE");

  waiting = await _NFTExchangeV1Proxy.setBuyHandler(_BuyHandler.target);
  await waiting.wait();
  console.log(`Buy Handler: ${await _NFTExchangeV1Proxy.buyHandler()}`);

  waiting = await _NFTExchangeV1Proxy.setSellHandler(_SellHandler.target);
  await waiting.wait();
  console.log(`Sell Handler: ${await _NFTExchangeV1Proxy.sellHandler()}`);

  waiting = await _NFTExchangeV1Proxy.setOfferHandler(_OfferHandler.target);
  await waiting.wait();
  console.log(`Offer Handler: ${await _NFTExchangeV1Proxy.offerHandler()}`);

  waiting = await _NFTExchangeV1Proxy.setCancelHandler(_CancelHandler.target);
  await waiting.wait();
  console.log(`Cancel Handler: ${await _NFTExchangeV1Proxy.cancelHandler()}`);

  waiting = await _NFTExchangeV1Proxy.setFeeUtils(_FeeUtils.target);
  await waiting.wait();
  console.log(`Fee Utils: ${await _NFTExchangeV1Proxy.feeUtils()}`);

  waiting = await _NFTExchangeV1Proxy.setMetaHandler(_MetaHandler.target);
  await waiting.wait();
  console.log(`Meta Handler: ${await _NFTExchangeV1Proxy.metaHandler()}`);

  waiting = await _NFTExchangeV1Proxy.setAirdropHandler(_AirdropHandler.target);
  await waiting.wait();
  console.log(`Airdrop Handler: ${await _NFTExchangeV1Proxy.airdropHandler()}`);

  waiting = await _NFTExchangeV1Proxy.setSubscribeHandler(_SubscribeHandler.target);
  await waiting.wait();
  console.log(`Subscribe Handler: ${await _NFTExchangeV1Proxy.subscribeHandler()}`);

  waiting = await _NFTExchangeV1Proxy.setBoxUtils(_OpenBox.target);
  await waiting.wait();
  console.log(`OpenBox Handler: ${await _NFTExchangeV1Proxy.boxUtils()}`);

  waiting = await _NFTExchangeV1Proxy.setBurnRedeemHandler(_BurnRedeemHandler.target);
  await waiting.wait();
  console.log(`BurnRedeemHandler Handler: ${await _NFTExchangeV1Proxy.burnRedeemHandler()}`);
}

// We recommend this pattern to be able to use async/await everywhere
// and properly handle errors.
main().catch((error) => {
  console.error(error);
  process.exitCode = 1;
});
