const { ethers } = require("hardhat");
const proxy = require("../../config/proxy");

const deploy = async (network, env) => {
  const handler_factory = await ethers.getContractFactory("SubscribeHandler");
  const handler_contract = await handler_factory.deploy();
  await handler_contract.deployed();

  console.log(`SubscribeHandler deployed at ${handler_contract.address}`);

  const proxy_addr = proxy[network][env];

  const exchange_contract = await ethers.getContractAt(
    "NFTExchangeV1",
    proxy_addr
  );
  console.log(`Proxy: ${exchange_contract.address}`);

  const setting = await exchange_contract.setSubscribeHandler(
    handler_contract.address
  );
  await setting.wait();

  console.log(
    `SubscribeHandler was set to ${await exchange_contract.subscribeHandler()}`
  );
};

deploy(process.env.NETWORK, process.env.ENV);
