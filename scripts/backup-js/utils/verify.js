const { exec } = require("child_process");

function verifyContract(contract_address, network) {
  console.log(`npx hardhat verify ${contract_address} --network ${network}`);
  console.log(`Verifying...`);
  exec(
    `npx hardhat verify ${contract_address} --network ${network}`,
    (error, stdout, stderr) => {
      if (error) {
        console.error(error.message);
        return;
      }
      if (stderr) {
        console.error(stderr);
        return;
      }

      console.log(stdout);
    }
  );
}

function getActualNetwork(network) {
  switch (network) {
    case "ETH":
      return "goerli";
    case "BSC":
      return "bsctestnet";
    case "POLYGON":
      return "mumbai";
  }
}

module.exports = { verifyContract, getActualNetwork };
