// Contracts
const BUY_HANDLER = "BUY_HANDLER";
const SELL_HANDLER = "SELL_HANDLER";
const OFFER_HANDLER = "OFFER_HANDLER";
const CANCEL_HANDLER = "CANCEL_HANDLER";
const AIRDROP_HANDLER = "AIRDROP_HANDLER";
const EXCHANGE = "EXCHANGE";
const PROXY = "PROXY";
const META_HANDLER = "META_HANDLER";
const NFTIFY721 = "NFTIFY721";
const NFTIFY1155 = "NFTIFY1155";
const FEE_UTILS = "FEE_UTILS";
const BOX_UTILS = "BOX_UTILS";

// Network
const ETHEREUM = "ethereum";
const BSC = "bsc";
const POLYGON = "polygon";

const RINKEBY = 4;
const BSCTESTNET = 97;
const MUMBAI = 80001;

// const SIGNER = "******************************************";

const SIGNER = "******************************************";

const COLLECTIONS_721 = {
  MUMBAI: [
    "******************************************",
    "******************************************",
    "******************************************",
  ],
  BSCTESTNET: [
    "******************************************",
    "******************************************",
    "******************************************",
  ],
};

const COLLECTIONS_1155 = {
  MUMBAI: [
    "******************************************",
    "******************************************",
    "******************************************",
  ],
  BSCTESTNET: [
    "******************************************",
    "******************************************",
    "******************************************",
  ],
};

const AIRDROP_CLAIM_REQUEST =
  "0x3485cac86bdaef5f0da0b3e2c58b4e6f6f2a693368a78cd7f08602160d3ae9df";

const TEN_DAYS = 60 * 60 * 24 * 10;
module.exports = {
  BUY_HANDLER,
  SELL_HANDLER,
  OFFER_HANDLER,
  CANCEL_HANDLER,
  AIRDROP_HANDLER,
  EXCHANGE,
  PROXY,
  META_HANDLER,
  NFTIFY721,
  NFTIFY1155,
  BOX_UTILS,
  FEE_UTILS,
  ETHEREUM,
  BSC,
  POLYGON,
  RINKEBY,
  BSCTESTNET,
  MUMBAI,
  SIGNER,
  COLLECTIONS_1155,
  COLLECTIONS_721,
  AIRDROP_CLAIM_REQUEST,
  TEN_DAYS,
};
