const { ethers, network } = require("hardhat");
const fs = require('fs');
const contracts = require('../deploy/data/contracts.json');

const defaultDeployOption = {
    contractPath: "",
    contractName: "",
    contractParams: [""],
};

const deployMulti = async (deployOps = [defaultDeployOption]) => {

    [deployer] = await ethers.getSigners();

    console.log("Deploying contracts with the account: ", deployer.address);

    const deployContracts = {};

    for (let i = 0; i < deployOps.length; i++) {
        op = deployOps[i];
        const name = (op.contractPath != null && op.contractPath != "") ? op.contractPath : op.contractName;
        // Deploy contract
        console.log(`deploying contract ${op.contractName}...`);
        const contractFactory = await ethers.getContractFactory(name);

        let deployedContract = await contractFactory.deploy(...op.contractParams);
        console.log(`${op.contractName} deployed at: ${deployedContract.address}`);
        Object.assign(deployContracts, {
            [op.contractName]: deployedContract.address,
        });
    }


    const data = {
        ...contracts,
        [network.name]: {
            ...contracts[network.name],
            ...deployContracts
        }
    };
    fs.writeFile('./scripts/deploy/data/contracts.json', JSON.stringify(data, null, 2), (err) => {
        if (err) {
            throw err;
        }
        console.log("JSON data is saved.");
    });
};

module.exports = { deployMulti };