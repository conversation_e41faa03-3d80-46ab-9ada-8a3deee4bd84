const { ethers, network } = require("hardhat");
const fs = require('fs');
const contracts = require('./data/contracts.json');

async function main() {

    [deployer] = await ethers.getSigners();

    console.log("Deploying contracts with the account: ", deployer.address);

    // Deploy SubscribeHandler
    const SUBSCRIBE_HANDLER = await ethers.getContractFactory("SubscribeHandler");
    const _SUBSCRIBE_HANDLER = await SUBSCRIBE_HANDLER.deploy();
    console.log(`Tx hash: ${_SUBSCRIBE_HANDLER.hash}`);
    console.log(`Subscribe Handler: ${_SUBSCRIBE_HANDLER.address}`);

    const data = {
        ...contracts,
        [network.name]: {
            ...contracts[network.name],
            SubscribeHandler: _SUBSCRIBE_HANDLER.address,
        }
    };
    fs.writeFile('./scripts/deploy/data/contracts.json', JSON.stringify(data, null, 2), (err) => {
        if (err) {
            throw err;
        }
        console.log("JSON data is saved.");
    });
}

main().then(async () => {
    // process.exit();
}).catch((error) => {
    console.error(error);
    process.exit(1);
})