const { ethers, network } = require("hardhat");
const fs = require('fs');
const contracts = require('./data/contracts.json');

async function main() {

    [deployer] = await ethers.getSigners();

    console.log("Deploying contracts with the account: ", deployer.address);

    // Deploy NFTExchangeV1
    const NFTExchangeV1 = await ethers.getContractFactory("NFTExchangeV1");
    let _NFTExchangeV1 = await NFTExchangeV1.deploy();
    console.log(`NFTExchangeV1: ${_NFTExchangeV1.address}`);

    const data = {
        ...contracts,
        [network.name]: {
            ...contracts[network.name],
            NFTExchangeV1: _NFTExchangeV1.address,
        }
    };
    fs.writeFile('./scripts/deploy/data/contracts.json', JSON.stringify(data, null, 2), (err) => {
        if (err) {
            throw err;
        }
        console.log("JSON data is saved.");
    });
}

main().then(async () => {
    // process.exit();
}).catch((error) => {
    console.error(error);
    process.exit(1);
})