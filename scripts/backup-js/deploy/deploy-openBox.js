const { ethers, network } = require("hardhat");
const fs = require('fs');
const contracts = require('./data/contracts.json');
const { deployMulti } = require('../utils/deployer');

async function main() {
    await deployMulti([
        {
            contractName: "OpenBox",
            contractParams: [],
        },
    ]);

    // Deploy cancel handler
    const OpenBox = await ethers.getContractFactory("OpenBox");
    const _OpenBox = await OpenBox.deploy();

    const NFTExchangeProxy = contracts[network.name].NFTExchangeProxy;
    const _NFTExchangeProxy = await ethers.getContractAt(
        "NFTExchangeV1",
        NFTExchangeProxy
    );
    let waiting = await _NFTExchangeProxy.connect(deployer).setBoxUtils(_OpenBox.address);
    await waiting.wait();
    console.log(`tx hash: ${waiting.hash}`);
    console.log(`OpenBox Handler: ${await _NFTExchangeProxy.boxUtils()}`);
}

main().then(async () => {
    // process.exit();
}).catch((error) => {
    console.error(error);
    process.exit(1);
})