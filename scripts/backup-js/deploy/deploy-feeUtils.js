const { ethers, network } = require("hardhat");
const fs = require('fs');
const contracts = require('./data/contracts.json');

async function main() {

    [deployer] = await ethers.getSigners();

    console.log("Deploying contracts with the account: ", deployer.address);

    // Deploy SubscribeHandler
    const FEE_UTILS = await ethers.getContractFactory("FeeUtils");
    const _FEE_UTILS = await FEE_UTILS.deploy();
    console.log(`Tx hash: ${_FEE_UTILS.hash}`);
    console.log(`FeeUtils Handler: ${_FEE_UTILS.address}`);

    const data = {
        ...contracts,
        [network.name]: {
            ...contracts[network.name],
            FeeUtils: _FEE_UTILS.address,
        }
    };
    fs.writeFile('./scripts/deploy/data/contracts.json', JSON.stringify(data, null, 2), (err) => {
        if (err) {
            throw err;
        }
        console.log("JSON data is saved.");
    });
}

main().then(async () => {
    // process.exit();
}).catch((error) => {
    console.error(error);
    process.exit(1);
})