const { ethers, network } = require("hardhat");
const fs = require('fs');
const contracts = require('./data/contracts.json');
const { deployMulti } = require('../utils/deployer');

async function main() {
    await deployMulti([
        {
            contractPath: "contracts/collections/NFTify721.sol:NFTify721",
            contractName: "NFTify721",
            contractParams: ["NFTify721", "N721", "https://metadata.ekoios.net/ipfs/", "******************************************"]
        },
        {
            contractPath: "contracts/collections/NFTify1155.sol:NFTify1155",
            contractName: "NFTify1155",
            contractParams: ["NFTify1155", "N1155", "https://metadata.ekoios.net/ipfs/", "******************************************"]
        },
        {
            contractName: "N1",
            contractParams: [],
        },
    ]);
}

main().then(async () => {
    // process.exit();
}).catch((error) => {
    console.error(error);
    process.exit(1);
})