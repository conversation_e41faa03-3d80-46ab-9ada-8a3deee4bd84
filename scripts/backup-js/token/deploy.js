const { ethers, network } = require("hardhat");
// const { verifyContract } = require("../utils/verify");
const fs = require('fs');
const contracts = require('../deploy/data/contracts.json');

const main = async () => {
  const usdc_factory = await ethers.getContractFactory("USDCoin");
  const usdc_contract = await usdc_factory.deploy();
  await usdc_contract.deployed();

  console.log(`USDC deployed to ${usdc_contract.address}`);

  // verifyContract(usdc_contract.address, network);

  const usdt_factory = await ethers.getContractFactory("Tether");
  const usdt_contract = await usdt_factory.deploy();
  await usdt_contract.deployed();

  console.log(`USDT deployed to ${usdt_contract.address}`);
  // verifyContract(usdt_contract.address, network);

  const data = {
    ...contracts,
    [network.name]: {
      ...contracts[network.name],
      USDC: usdc_contract.address,
      USDT: usdt_contract.address,
    }
  };
  fs.writeFile('./scripts/deploy/data/contracts.json', JSON.stringify(data, null, 2), (err) => {
    if (err) {
      throw err;
    }
    console.log("JSON data is saved.");
  });
};

main().then(async () => {
  // process.exit();
}).catch((error) => {
  console.error(error);
  process.exit(1);
})