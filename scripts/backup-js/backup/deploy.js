const hre = require("hardhat");
const { deployContract } = require("./utils/helpers");
require("dotenv").config();

/**
 * ? -- NOTE --
 * 1. BUY_HANDLER: no params
 * 2. SELL_HANDLER: no params
 * 3. OFFER_HANDLER: no params
 * 4. CANCEL_HANDLER: no params
 * 5. META_HANDLER: need PROXY's address
 * 6. AIRDROP_HANDLER: no params
 * 7. EXCHANGE: no params
 * 8. PROXY: no params
 * 9. NFTIFY721: [name, symbol, uri, controller]
 * 10. NFTIFY1155: [name, symbol, uri, controller]
 *
 */
const main = async () => {
  await hre.run("compile");

  const [, , contract, ...params] = process.argv;
  console.log(params);
  console.log("Deploying contract for:", contract);
  return await deployContract(contract, [...params]);
};

main()
  .then(() => {
    console.log;
    process.exit(0);
  })
  .catch((err) => {
    console.log(err);
    process.exit(1);
  });
