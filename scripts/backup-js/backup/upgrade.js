const { ethers } = require("hardhat");
const proxy = require("../config/proxy");

const { exec } = require("child_process");

const getActualNetwork = (network) => {
  switch (network) {
    case "ETH":
      return "goerli";
    case "BSC":
      return "bsctestnet";
    case "POLYGON":
      return "mumbai";
  }
};

const upgrade = async (network, env) => {
  console.log(`Upgrading proxy contract on ${network}:${env}`);

  let proxy_addr = proxy[network][env];

  const exchange_factory = await ethers.getContractFactory("NFTExchangeV1");
  const exchange_contract = await exchange_factory.deploy();
  await exchange_contract.deployed();

  console.log(`Exchange contract was deployed to ${exchange_contract.address}`);

  console.log(`Verifying...`);
  exec(
    `npx hardhat verify ${
      exchange_contract.address
    } --network ${getActualNetwork(network)}`,
    (error, stdout, stderr) => {
      if (error) {
        console.error(error.message);
        return;
      }
      if (stderr) {
        console.error(stderr);
        return;
      }

      console.log(stdout);
    }
  );

  const proxy_contract = await ethers.getContractAt(
    "NFTExchangeProxy",
    proxy_addr
  );
  console.log(`Proxy: ${proxy_contract.address}`);

  console.log(
    `Upgrading proxy from ${await proxy_contract.implementation()} to ${
      exchange_contract.address
    }`
  );
  const upgrading = await proxy_contract.upgradeTo(exchange_contract.address);
  await upgrading.wait();

  console.log(`Upgraded to ${await proxy_contract.implementation()}`);
};

upgrade(process.env.NETWORK, process.env.ENV);
