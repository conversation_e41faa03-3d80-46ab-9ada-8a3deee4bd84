const { ethers } = require("hardhat");
const {
    SIGNER,
} = require("../utils/constants");
const fs = require('fs');
const contracts = require('../deploy/data/contracts.json');


async function main() {

    [deployer] = await ethers.getSigners();

    console.log("Deploying contracts with the account: ", deployer.address);

    const NFTExchangeV1 = await ethers.getContractFactory("NFTExchangeV1");
    let _NFTExchangeV1 = await NFTExchangeV1.deploy();
    console.log(`NFTExchangeV1: ${_NFTExchangeV1.address}`);

    const NFTExchangeProxy = await ethers.getContractFactory("NFTExchangeProxy");
    const _NFTExchangeProxy = await NFTExchangeProxy.deploy();
    console.log(`NFTExchangeProxy: ${_NFTExchangeProxy.address}`);

    // Upgrade
    await _NFTExchangeProxy.upgradeTo(_NFTExchangeV1.address);
    console.log(`Implementation: ${await _NFTExchangeProxy.implementation()}`);

    // Set admin
    console.log(`Proxy admin: ${await _NFTExchangeProxy.proxyOwner()}, deployer: ${deployer.address}`)
    let waiting = await _NFTExchangeProxy.setAdminList(deployer.address, true);
    await waiting.wait();
    console.log("Set admin list - DONE");

    _NFTExchangeV1 = NFTExchangeV1.attach(_NFTExchangeProxy.address);


    console.log(
        `Now the wallet address ${deployer.address
        } is admin: ${await _NFTExchangeV1.isAdmin(deployer.address)}`
    );

    // Set recipient
    waiting = await _NFTExchangeV1.setRecipient(deployer.address);
    await waiting.wait();
    console.log(`Recipient: ${await _NFTExchangeV1.recipient()}`);

    // Set signer
    waiting = await _NFTExchangeV1.setSigner(SIGNER, true);
    await waiting.wait();
    console.log("Set signer - DONE");

    // Deploy buy handler
    const BUY_HANDLER_ = await ethers.getContractFactory("BuyHandler");
    const _BUY_HANDLER_ = await BUY_HANDLER_.deploy();
    waiting = await _NFTExchangeV1.setBuyHandler(_BUY_HANDLER_.address);
    await waiting.wait();
    console.log(`Buy Handler: ${await _NFTExchangeV1.buyHandler()}`);

    // Deploy sell handler
    const SELL_HANDLER_ = await ethers.getContractFactory("SellHandler");
    const _SELL_HANDLER_ = await SELL_HANDLER_.deploy();
    waiting = await _NFTExchangeV1.setSellHandler(_SELL_HANDLER_.address);
    await waiting.wait();
    console.log(`Sell Handler: ${await _NFTExchangeV1.sellHandler()}`);

    // Deploy offer handler
    const OFFER_HANDLER_ = await ethers.getContractFactory("OfferHandler");
    const _OFFER_HANDLER_ = await OFFER_HANDLER_.deploy();
    waiting = await _NFTExchangeV1.setOfferHandler(_OFFER_HANDLER_.address);
    await waiting.wait();
    console.log(`Offer Handler: ${await _NFTExchangeV1.offerHandler()}`);

    // Deploy cancel handler
    const CANCEL_HANDLER_ = await ethers.getContractFactory("CancelHandler");
    const _CANCEL_HANDLER_ = await CANCEL_HANDLER_.deploy();
    waiting = await _NFTExchangeV1.setCancelHandler(_CANCEL_HANDLER_.address);
    await waiting.wait();
    console.log(`Cancel Handler: ${await _NFTExchangeV1.cancelHandler()}`);

    // Deploy fee utils
    const FEE_UTILS_ = await ethers.getContractFactory("FeeUtils");
    const _FEE_UTILS_ = await FEE_UTILS_.deploy();
    waiting = await _NFTExchangeV1.setFeeUtils(_FEE_UTILS_.address);
    await waiting.wait();
    console.log(`Fee Utils: ${await _NFTExchangeV1.feeUtils()}`);

    // Deploy meta handler
    const META_HANDLER_ = await ethers.getContractFactory("MetaHandler");
    const _META_HANDLER_ = await META_HANDLER_.deploy(_NFTExchangeProxy.address);
    waiting = await _NFTExchangeV1.setMetaHandler(_META_HANDLER_.address);
    await waiting.wait();
    console.log(`Meta Handler: ${await _NFTExchangeV1.metaHandler()}`);

    // Deploy airdrop handler
    const AIRDROP_HANDLER_ = await ethers.getContractFactory("AirdropHandler");
    const _AIRDROP_HANDLER_ = await AIRDROP_HANDLER_.deploy();
    waiting = await _NFTExchangeV1.setAirdropHandler(_AIRDROP_HANDLER_.address);
    await waiting.wait();
    console.log(`Airdrop Handler: ${await _NFTExchangeV1.airdropHandler()}`);

    // deploy SubscribeHandler
    const SUBSCRIBE_HANDLER = await ethers.getContractFactory("SubscribeHandler");
    const _SUBSCRIBE_HANDLER = await SUBSCRIBE_HANDLER.deploy();
    waiting = await _NFTExchangeV1.setSubscribeHandler(_SUBSCRIBE_HANDLER.address);
    await waiting.wait();
    console.log(`Subscribe Handler: ${await _NFTExchangeV1.subscribeHandler()}`);

    const data = {
        ...contracts,
        [network.name]: {
            ...contracts[network.name],
            NFTExchangeV1: _NFTExchangeV1.address,
            NFTExchangeProxy: _NFTExchangeProxy.address,
            Implementation: await _NFTExchangeProxy.implementation(),
            BuyHandler: _BUY_HANDLER_.address,
            SellHandler: _SELL_HANDLER_.address,
            OfferHandler: _OFFER_HANDLER_.address,
            CancelHandler: _CANCEL_HANDLER_.address,
            FeeUtils: _FEE_UTILS_.address,
            MetaHandler: _META_HANDLER_.address,
            AirdropHandler: _AIRDROP_HANDLER_.address,
            SubscribeHandler: _SUBSCRIBE_HANDLER.address,
        }
    };
    fs.writeFile('./scripts/deploy/data/contracts.json', JSON.stringify(data, null, 2), (err) => {
        if (err) {
            throw err;
        }
        console.log("JSON data is saved.");
    });
}

main().then(async () => {
    // process.exit();
}).catch((error) => {
    console.error(error);
    process.exit(1);
})
