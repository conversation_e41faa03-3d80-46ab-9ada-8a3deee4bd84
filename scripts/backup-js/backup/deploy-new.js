const hre = require("hardhat");
const { deployContract, loadContract } = require("./utils/helpers");
const {
  PROXY,
  EXCHANGE,
  SIGNER,
  BUY_HANDLER,
  SELL_HANDLER,
  OFFER_HANDLER,
  <PERSON><PERSON><PERSON>_HANDLER,
  FEE_UTILS,
  BOX_UTILS,
  META_HANDLER,
  AIRDROP_HANDLER,
} = require("./utils/constants");
require("dotenv").config();

/**
 * ? -- NOTE --
 * 1. BUY_HANDLER: no params
 * 2. SELL_HANDLER: no params
 * 3. OFFER_HANDLER: no params
 * 4. CANCEL_HANDLER: no params
 * 5. META_HANDLER: need PROXY's address
 * 6. AIRDROP_HANDLER: no params
 * 7. EXCHANGE: no params
 * 8. PROXY: no params
 * 9. NFTIFY721: [name, symbol, uri, controller]
 * 10. NFTIFY1155: [name, symbol, uri, controller]
 *
 */
const main = async () => {
  await hre.run("compile");

  [deploy_wallet] = await hre.ethers.getSigners();

  // PROXY
  const _PROXY = await deployContract(PROXY);

  // EXCHANGE
  let _EXCHANGE = await deployContract(EXCHANGE);

  // Upgrade
  let waiting = await _PROXY.upgradeTo(_EXCHANGE.address);
  await waiting.wait();
  console.log("Upgraded - ", await _PROXY.implementation());

  // Set admin
  waiting = await _PROXY.setAdminList(deploy_wallet.address, true);
  await waiting.wait();
  console.log("Set admin list - DONE");

  // Load exchange from proxy
  _EXCHANGE = await loadContract(EXCHANGE, _PROXY.address);
  // console.log(_EXCHANGE);

  console.log(
    `Now the wallet address ${
      deploy_wallet.address
    } is admin: ${await _EXCHANGE.isAdmin(deploy_wallet.address)}`
  );

  // Set recipient
  waiting = await _EXCHANGE.setRecipient(deploy_wallet.address);
  await waiting.wait();
  console.log(`Recipient: ${await _EXCHANGE.recipient()}`);

  // Set signer
  waiting = await _EXCHANGE.setSigner(SIGNER, true);
  await waiting.wait();
  console.log("Set signer - DONE");

  // Deploy buy handler
  const BUY_HANDLER_ = await deployContract(BUY_HANDLER);
  waiting = await _EXCHANGE.setBuyHandler(BUY_HANDLER_.address);
  await waiting.wait();
  console.log(`Buy Handler: ${await _EXCHANGE.buyHandler()}`);

  // Deploy sell handler
  const SELL_HANDLER_ = await deployContract(SELL_HANDLER);
  waiting = await _EXCHANGE.setSellHandler(SELL_HANDLER_.address);
  await waiting.wait();
  console.log(`Sell Handler: ${await _EXCHANGE.sellHandler()}`);

  // Deploy offer handler
  const OFFER_HANDLER_ = await deployContract(OFFER_HANDLER);
  waiting = await _EXCHANGE.setOfferHandler(OFFER_HANDLER_.address);
  await waiting.wait();
  console.log(`Offer Handler: ${await _EXCHANGE.offerHandler()}`);

  // Deploy cancel handler
  const CANCEL_HANDLER_ = await deployContract(CANCEL_HANDLER);
  waiting = await _EXCHANGE.setCancelHandler(CANCEL_HANDLER_.address);
  await waiting.wait();
  console.log(`Cancel Handler: ${await _EXCHANGE.cancelHandler()}`);

  // Deploy fee utils
  const FEE_UTILS_ = await deployContract(FEE_UTILS);
  waiting = await _EXCHANGE.setFeeUtils(FEE_UTILS_.address);
  await waiting.wait();
  console.log(`Fee Utils: ${await _EXCHANGE.feeUtils()}`);

  // Deploy box utils
  const BOX_UTILS_ = await deployContract(BOX_UTILS);
  waiting = await _EXCHANGE.setBoxUtils(BOX_UTILS_.address);
  await waiting.wait();
  console.log(`Box Utils: ${await _EXCHANGE.boxUtils()}`);

  // Deploy meta handler
  const META_HANDLER_ = await deployContract(META_HANDLER, [_PROXY.address]);
  waiting = await _EXCHANGE.setMetaHandler(META_HANDLER_.address);
  await waiting.wait();
  console.log(`Meta Handler: ${await _EXCHANGE.metaHandler()}`);

  // Deploy airdrop handler
  const AIRDROP_HANDLER_ = await deployContract(AIRDROP_HANDLER);
  waiting = await _EXCHANGE.setAirdropHandler(AIRDROP_HANDLER_.address);
  await waiting.wait();
  console.log(`Airdrop Handler: ${await _EXCHANGE.airdropHandler()}`);

  console.log(`---- DONE ----`);
};

main()
  .then(() => {
    console.log;
    process.exit(0);
  })
  .catch((err) => {
    console.log(err);
    process.exit(1);
  });
