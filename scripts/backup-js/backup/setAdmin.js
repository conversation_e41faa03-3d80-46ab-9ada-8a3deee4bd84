const { ethers, upgrades, network } = require("hardhat");
const contracts = require("../deploy/data/contracts.json");
const env = process.env.ENV;

async function main() {
    // const NFTExchangeProxy = contracts[network.name].NFTExchangeProxy;

    [deployer] = await ethers.getSigners();
    console.log("Deploying contracts with the account: ", deployer.address);
    
    const _NFTExchangeProxy = await ethers.getContractAt(
        "NFTExchangeV1",
        "******************************************"
    );
    let waiting = await _NFTExchangeProxy.connect(deployer).setSigner("******************************************", true);
    await waiting.wait();
    console.log(`tx hash: ${waiting.hash}`);

}

main().then(async () => {
    // process.exit();
}).catch((error) => {
    console.error(error);
    process.exit(1);
})