const { ethers, network } = require("hardhat");
const provider = ethers.provider;
const {
    SIGNER,
} = require("../utils/constants");
const proxy = require("../../../config/proxy");

const getProxyAddress = () => {
    switch (network.name) {
        case "bscMainnet":
            return proxy.BSC.dev;
        case "bscTestnet":
            break;
        case "polygon":
            return proxy.POLYGON.dev;
        case "mumbai":
            break;
        case "eth":
            return proxy.ETH.dev;
        case "goerli":
            return "******************************************";
        default:
            break;
    }
};


async function main() {

    [deployer] = await ethers.getSigners();

    console.log("Deploying contracts with the account: ", deployer.address);

    const NFTExchangeV1 = await ethers.getContractFactory("NFTExchangeV1");
    const NFTExchangeV1DeployData = NFTExchangeV1.getDeployTransaction();
    let _NFTExchangeV1GasLimit = await provider.estimateGas(NFTExchangeV1DeployData);
    console.log(`NFTExchangeV1 gas limit: ${_NFTExchangeV1GasLimit}`);

    const NFTExchangeProxy = await ethers.getContractFactory("NFTExchangeProxy");
    const NFTExchangeProxyDeployData = NFTExchangeProxy.getDeployTransaction();
    let _NFTExchangeProxyGasLimit = await provider.estimateGas(NFTExchangeProxyDeployData);
    console.log(`NFTExchangeProxy gas limit: ${_NFTExchangeProxyGasLimit}`);

    // Deploy buy handler
    const BUY_HANDLER = await ethers.getContractFactory("BuyHandler");
    const BUY_HANDLERDeployData = BUY_HANDLER.getDeployTransaction();
    let _BUY_HANDLERGasLimit = await provider.estimateGas(BUY_HANDLERDeployData);
    console.log(`BUY_HANDLER gas limit: ${_BUY_HANDLERGasLimit}`);


    // Deploy sell handler
    const SELL_HANDLER = await ethers.getContractFactory("SellHandler");
    const SELL_HANDLERDeployData = SELL_HANDLER.getDeployTransaction();
    let _SELL_HANDLERGasLimit = await provider.estimateGas(SELL_HANDLERDeployData);
    console.log(`SELL_HANDLER gas limit: ${_SELL_HANDLERGasLimit}`);


    // Deploy offer handler
    const OFFER_HANDLER = await ethers.getContractFactory("OfferHandler");
    const OFFER_HANDLERDeployData = OFFER_HANDLER.getDeployTransaction();
    let _OFFER_HANDLERGasLimit = await provider.estimateGas(OFFER_HANDLERDeployData);
    console.log(`OFFER_HANDLER gas limit: ${_OFFER_HANDLERGasLimit}`);


    // Deploy cancel handler
    const CANCEL_HANDLER_ = await ethers.getContractFactory("CancelHandler");
    const CANCEL_HANDLER_DeployData = CANCEL_HANDLER_.getDeployTransaction();
    let _CANCEL_HANDLER_GasLimit = await provider.estimateGas(CANCEL_HANDLER_DeployData);
    console.log(`CANCEL_HANDLER_ gas limit: ${_CANCEL_HANDLER_GasLimit}`);


    // Deploy fee utils
    const FEE_UTILS_ = await ethers.getContractFactory("FeeUtils");
    const FEE_UTILS_DeployData = FEE_UTILS_.getDeployTransaction();
    let _FEE_UTILS_GasLimit = await provider.estimateGas(FEE_UTILS_DeployData);
    console.log(`FEE_UTILS_ gas limit: ${_FEE_UTILS_GasLimit}`);

    // Deploy meta handler
    const META_HANDLER_ = await ethers.getContractFactory("MetaHandler");
    const META_HANDLER_DeployData = META_HANDLER_.getDeployTransaction("******************************************");
    let _META_HANDLER_GasLimit = await provider.estimateGas(META_HANDLER_DeployData);
    console.log(`META_HANDLER_ gas limit: ${_META_HANDLER_GasLimit}`);


    // Deploy airdrop handler
    const AIRDROP_HANDLER_ = await ethers.getContractFactory("AirdropHandler");
    const AIRDROP_HANDLER_DeployData = AIRDROP_HANDLER_.getDeployTransaction();
    let _AIRDROP_HANDLER_GasLimit = await provider.estimateGas(AIRDROP_HANDLER_DeployData);
    console.log(`AIRDROP_HANDLER_ gas limit: ${_AIRDROP_HANDLER_GasLimit}`);


    // deploy SubscribeHandler
    const SUBSCRIBE_HANDLER = await ethers.getContractFactory("SubscribeHandler");
    const SUBSCRIBE_HANDLERDeployData = SUBSCRIBE_HANDLER.getDeployTransaction();
    let _SUBSCRIBE_HANDLERGasLimit = await provider.estimateGas(SUBSCRIBE_HANDLERDeployData);
    console.log(`SUBSCRIBE_HANDLER gas limit: ${_SUBSCRIBE_HANDLERGasLimit}`);

    const proxyAddress = getProxyAddress();

    // Upgrade
    const _NFTExchangeProxy = await ethers.getContractAt(
        "NFTExchangeProxy",
        proxyAddress
    );
    const ProxyUpgradeGasLimit = await _NFTExchangeProxy.estimateGas.upgradeTo("******************************************");
    console.log(`Proxy upgrade implementation: ${ProxyUpgradeGasLimit}`);

    // Set admin
    let setAdminListGasLimit = await _NFTExchangeProxy.estimateGas.setAdminList(deployer.address, true);
    console.log(`set admin list: ${setAdminListGasLimit}`);

    const _NFTExchangeV1 = await ethers.getContractAt(
        "NFTExchangeV1",
        proxyAddress
    );

    // Set recipient
    const setRecipientGasLimit = await _NFTExchangeV1.estimateGas.setRecipient(deployer.address);
    console.log(`set Recipient: ${setRecipientGasLimit}`);

    // Set signer
    const setSignerGaslimit = await _NFTExchangeV1.estimateGas.setSigner(SIGNER, true);
    console.log(`Set signer: ${setSignerGaslimit}`);

    const setBuyHandlerGasLimit = await _NFTExchangeV1.estimateGas.setBuyHandler("******************************************");
    console.log(`Set Buy Handler: ${setBuyHandlerGasLimit}`);

    const setSellHandlerGasLimit = await _NFTExchangeV1.estimateGas.setSellHandler("******************************************");
    console.log(`Set Sell Handler: ${setSellHandlerGasLimit}`);

    const setOfferHandlerGasLimit = await _NFTExchangeV1.estimateGas.setOfferHandler("******************************************");
    console.log(`Set Offer Handler: ${setOfferHandlerGasLimit}`);

    const setCancelHandlerGasLimit = await _NFTExchangeV1.estimateGas.setCancelHandler("0x65B143c619DeF384Bd0C59E6674D02BA364367b4");
    console.log(`Set Cancel Handler: ${setCancelHandlerGasLimit}`);

    const setFeeUtilsGasLimit = await _NFTExchangeV1.estimateGas.setFeeUtils("0xD8e16E32AA7eFb7AE1a5A80f207fE16B34039cC1");
    console.log(`Set Fee Utils: ${setFeeUtilsGasLimit}`);

    const setMetaHandlerGasLimit = await _NFTExchangeV1.estimateGas.setMetaHandler("0x97C9FFDE1B4028C0d8C45FF7ED6DCd7819eF6D79");
    console.log(`Set Meta Handler: ${setMetaHandlerGasLimit}`);

    const setAirdropHandlerGasLimit = await _NFTExchangeV1.estimateGas.setAirdropHandler("0x21aCD8d4Da100a99CD7f1469B0B64394DA2F82df");
    console.log(`Set Airdrop Handler: ${setAirdropHandlerGasLimit}`);

    const setSubscribeHandler = await _NFTExchangeV1.estimateGas.setSubscribeHandler("0x96a8cC39CEA5aB1DE204091d8E414821fB6A9AD5");
    console.log(`Set Subscribe Handler: ${setSubscribeHandler}`);

}

main().then(async () => {
    process.exit();
}).catch((error) => {
    console.error(error);
    process.exit(1);
})