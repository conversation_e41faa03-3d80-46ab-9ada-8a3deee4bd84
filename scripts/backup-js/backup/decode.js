const { ethers } = require("hardhat");
const abiDecoder = require('abi-decoder');
const fs =  require('fs');


// address payer,
// address recipient,
// address token,
// uint256 amount,
// bytes calldata _id,
// bytes calldata signature
// const data = "0x08c379a00000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000002853756273637269626548616e646c65723a204f6e6c792061646d696e2773207369676e6174757265000000000000000000000000000000000000000000000000";

// const res = ethers.utils.defaultAbiCoder.decode(
//   ['address', 'address', 'address', 'uint256', 'bytes', 'bytes'],
//   ethers.utils.hexDataSlice(data, 6)
// )
// console.log(res)

// const iface = new ethers.utils.Interface(['function subscriptionPayment(address payer,address recipient,address token,uint256 amount,bytes calldata _id,bytes calldata signature'])

// const r = iface.decodeFunctionData('subscriptionPayment', '0x08c379a00000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000002853756273637269626548616e646c65723a204f6e6c792061646d696e2773207369676e6174757265000000000000000000000000000000000000000000000000')
// console.log(r);
// gives: [e, ["******************************************", "******************************************"], "******************************************", e] (4)


// const provider = new ethers.providers.JsonRpcProvider("https://mainnet.infura.io/v3/<key>");

async function main() {
    const tx = await ethers.provider.getTransaction("0xca4599f024ea23a9478814e45e6bbabc2cef356d9204947e7e41a87a16dfc9c9");

    const ERC20ABI = JSON.parse(fs.readFileSync('artifacts/contracts/src/exchanges/NFTExchangeV1.sol/NFTExchangeV1.json', "utf8"))['abi'];
    console.log(ERC20ABI)
    abiDecoder.addABI(ERC20ABI);

    const decoded = abiDecoder.decodeMethod(tx.data);
    console.log(decoded);
}

main();