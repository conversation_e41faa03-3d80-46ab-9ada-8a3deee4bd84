const { network } = require("hardhat");
const { verifyContract } = require("../utils/verify");
const contracts = require('../deploy/data/contracts.json');

async function main() {
    const NFTExchangeV1 = contracts[network.name].NFTExchangeV1;
    verifyContract(NFTExchangeV1, network.name);
}

main().then(async () => {
    // process.exit();
}).catch((error) => {
    console.error(error);
    process.exit(1);
})