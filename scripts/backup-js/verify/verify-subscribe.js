const { network } = require("hardhat");
const { verifyContract } = require("../utils/verify");
const contracts = require('../deploy/data/contracts.json');

async function main() {
    const SubscribeHandler = contracts[network.name].SubscribeHandler;
    verifyContract(SubscribeHandler, network.name);
}

main().then(async () => {
    // process.exit();
}).catch((error) => {
    console.error(error);
    process.exit(1);
})