const hre = require("hardhat");
require("dotenv").config();

const {
  COLLECTIONS_1155,
  COLLECTIONS_721,
  EXCHANGE,
  AIRDROP_CLAIM_REQUEST,
  RINKEBY,
  BSCTESTNET,
  MUMBAI,
} = require("../utils/constants");
const {
  genRanHex,
  createSignature,
  loadProxyContract,
} = require("../utils/helpers");

const ERC_1155 = 1;
const ERC_721 = 2;

// Function for randomizing token ids
const randomTokenIds = (size) =>
  [...Array(size)].map(() => Math.floor(Math.random() * 1000 + 1).toString());

// Function for randomizing the types of each token id
const randomTypes = (size) =>
  [...Array(size)].map(() => (Math.random() > 0.5 ? ERC_1155 : ERC_721));

// Function for randomizing quantity of each token ids depending on collection's types
const randomQuantities = (types) => {
  let quantities = [];

  for (let i = 0; i < types.length; i++) {
    quantities[i] =
      types[i] == ERC_1155 ? Math.floor(Math.random() * 9 + 1) * 10 : 1;
  }

  return quantities;
};

// Function for randomizing the collection address for each token id depending on collection's types
const randomCollections = async (types) => {
  const chainId = (await hre.ethers.provider.getNetwork()).chainId;

  switch (chainId) {
    case RINKEBY:
      break;
    case BSCTESTNET:
      return _randomCollections(
        types,
        COLLECTIONS_1155.BSCTESTNET,
        COLLECTIONS_721.BSCTESTNET
      );
    case MUMBAI:
      return _randomCollections(
        types,
        COLLECTIONS_1155.MUMBAI,
        COLLECTIONS_721.MUMBAI
      );
  }
};

const _randomCollections = async (types, collections_1155, collections_721) => {
  const collections = [];

  for (let i = 0; i < types.length; i++) {
    collections[i] =
      types[i] == ERC_1155
        ? collections_1155[Math.floor(Math.random() * collections_1155.length)]
        : collections_721[Math.floor(Math.random() * collections_721.length)];
  }

  return collections;
};

// Function for signing airdrop event
const signAirdropEvent = async (data) => {
  const airdropEvent = {
    type: {
      creator: "address",
      eventId: "uint256",
      start: "uint256",
      end: "uint256",
      quantity: "uint256",
    },
    value: data,
  };

  return await createSignature(airdropEvent);
};

// Function for signing airdrop claim
const signAirdropClaim = async (data) => {
  const airdropClaim = {
    type: {
      receiver: "address",
      tokenIds: "uint256[]",
      amounts: "uint256[]",
      types: "uint256[]",
      quantities: "uint256[]",
      collections: "address[]",
      start: "uint256",
      end: "uint256",
      limitation: "uint256",
      nonce: "uint256",
      airdropEventSignature: "bytes",
    },
    value: data,
  };

  return await createSignature(airdropClaim);
};

// Function for creating airdrop event
const createAirdropEvent = async (
  size,
  time_limit,
  claim_limit,
  _start,
  _end
) => {
  // Get the wallet for creating and signing airdrop event
  const [wallet] = await hre.ethers.getSigners();

  // Airdrop event's duration
  const duration = 60 * 60 * 24 * 10; // 10 days
  // Timestamp that the event starts.
  const start =
    _start !== undefined ? _start : Math.floor(Date.now() / 1000) - 60 * 60;

  // Timestamp that the event ends. If there's no time limit, it's set to 0.
  const end = time_limit ? (_end !== undefined ? _end : start + duration) : 0;

  // Limitation to claim NFT. If there's no claim limit, it's set to 0.
  const limitation = claim_limit ? 10 : 0;

  // Random token ids.
  const tokenIds = randomTokenIds(size);

  // Random types for these token ids.
  const types = randomTypes(size);

  // Random quantites for these token ids with corresponding types.
  const quantities = randomQuantities(types);

  // Randdom collection's address for these token ids with corresponding types.
  const collections = await randomCollections(types);

  // Data to sign of the airdrop event.
  const signData = {
    creator: wallet.address,
    eventId: "0x" + genRanHex(24),
    start: start,
    end: end,
    quantity: quantities.reduce((a, b) => a + b, 0),
  };

  // Return data.
  return {
    start: start,
    end: end,
    limitation: limitation,
    tokenIds: tokenIds,
    initialQuantities: quantities,
    availableQuantities: quantities,
    collections: collections,
    types: types,
    airdropEventSignature: await signAirdropEvent(signData),
  };
};

// Function for creating a airdrop claim
const createAirdropClaim = async (airdropEvent, claimAmount) => {
  // Get the wallet for signing and claiming
  const [wallet] = await hre.ethers.getSigners();

  // Load the proxy contract to get nonce
  const proxy = await loadProxyContract();

  // Generate data for airdrop claim
  const airdropClaim = {
    receiver: wallet.address,
    tokenIds: [],
    amounts: [],
    types: [],
    quantities: [],
    collections: [],
    start: airdropEvent.start,
    end: airdropEvent.end,
    limitation: airdropEvent.limitation,
    nonce: (
      await proxy.getNonce(AIRDROP_CLAIM_REQUEST, wallet.address)
    ).toString(),
    airdropEventSignature: airdropEvent.airdropEventSignature,
  };

  // Start to random NFTs to claim.
  for (let i = 0; i < claimAmount; i++) {
    // Random an index from token ids array.
    const index = Math.floor(Math.random() * airdropEvent.tokenIds.length);

    // Get the token id
    const tokenId = airdropEvent.tokenIds[index];

    // If token id has already existed, just increase its amount in the airdrop claim data.
    // Else, push it into the airdrop claim data.
    if (airdropClaim.tokenIds.includes(tokenId)) {
      airdropClaim.amounts[airdropClaim.tokenIds.indexOf(tokenId)]++;
    } else {
      airdropClaim.tokenIds.push(tokenId);
      airdropClaim.amounts.push(1);
      airdropClaim.types.push(airdropEvent.types[index]);
      airdropClaim.quantities.push(airdropEvent.initialQuantities[index]);
      airdropClaim.collections.push(airdropEvent.collections[index]);
    }

    // If it's ERC1155, decrease the available quantitiy.
    if (airdropEvent.types[index] == 1)
      airdropEvent.availableQuantities[index]--;

    // If it's ERC721 or it's ERC1155 and there's no NFT left, remove it from the airdrop event
    if (
      (airdropEvent.availableQuantities[index] === 0 &&
        airdropEvent.types[index] === 1) ||
      airdropEvent.types[index] == 2
    ) {
      airdropEvent.tokenIds = removeFromArray(airdropEvent.tokenIds, index);
      airdropEvent.availableQuantities = removeFromArray(
        airdropEvent.availableQuantities,
        index
      );
      airdropEvent.initialQuantities = removeFromArray(
        airdropEvent.initialQuantities,
        index
      );
      airdropEvent.types = removeFromArray(airdropEvent.types, index);
      airdropEvent.collections = removeFromArray(
        airdropEvent.collections,
        index
      );
    }
  }

  // Return the data
  return {
    claimData: {
      data: [
        airdropClaim.start,
        airdropClaim.end,
        airdropClaim.limitation,
        ...airdropClaim.tokenIds,
        ...airdropClaim.amounts,
        ...airdropClaim.types,
        ...airdropClaim.quantities,
      ],
      addr: [
        airdropClaim.receiver,
        wallet.address,
        ...airdropClaim.collections,
      ],
      strs: [genRanHex(24)],
      signatures: [
        airdropClaim.airdropEventSignature,
        await signAirdropClaim(airdropClaim),
      ],
    },
    airdropEventUpdated: airdropEvent,
  };
};

const removeFromArray = (arr, index) => {
  if (!Array.isArray(arr)) return [];

  if (index === 0) return arr.slice(1);

  if (index === arr.length - 1) return arr.slice(0, arr.length - 1);

  return arr.slice(0, index).concat(arr.slice(index + 1));
};

module.exports = {
  createAirdropEvent,
  createAirdropClaim,
};
