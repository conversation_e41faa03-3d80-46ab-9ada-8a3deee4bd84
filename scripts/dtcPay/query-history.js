const axios = require("axios");
const crypto = require("crypto");

class DtcPaymentClient {
  constructor(config) {
    this.config = config;
  }

  /**
   * Generates the HMAC-SHA512 signature required for DTC API authentication
   */
  generateSignature(method, timestamp, url, body) {
    const urlPath = url.replace(this.config.baseUrl, "");
    let dataToSign = `${method}${timestamp}${urlPath}`;
    if (method.toUpperCase() === "POST" && body) {
      dataToSign += JSON.stringify(body);
    }
    const hmac = crypto.createHmac("sha512", this.config.signKey);
    hmac.update(dataToSign);
    return hmac.digest("base64");
  }

  /**
   * Creates the required DTC headers for the request
   */
  createHeaders(method, url, body) {
    const timestamp = Date.now().toString();
    const signature = this.generateSignature(method, timestamp, url, body);
    return {
      "D-MERCHANT-ID": this.config.merchantId,
      "D-TERMINAL-ID": this.config.terminalId,
      "D-TIMESTAMP": timestamp,
      "D-SIGNATURE": signature,
      "Content-Type": "application/json",
    };
  }

  /**
   * Query payment history from DTC
   * @param {Object} queryInfo - Query parameters (e.g., { pageSize, pageNo })
   * @returns {Promise<Object>} - Response from DTC
   */
  async queryHistory(queryInfo) {
    const url = `${this.config.baseUrl}/api/v1/query-history`;
    const method = "POST";
    const requestBody = {
      pageSize: queryInfo.pageSize,
      pageNo: queryInfo.pageNo,
    };
    
    const headers = this.createHeaders(method, url, requestBody);
    
    // Log all information needed for Postman
    console.log("=== POSTMAN CONFIGURATION ===");
    console.log("Method:", method);
    console.log("URL:", url);
    console.log("\nHeaders:");
    Object.entries(headers).forEach(([key, value]) => {
      console.log(`${key}: ${value}`);
    });
    console.log("\nRequest Body (raw JSON):");
    console.log(JSON.stringify(requestBody, null, 2));
    console.log("\n=== SIGNATURE GENERATION INFO ===");
    const timestamp = headers["D-TIMESTAMP"];
    const urlPath = url.replace(this.config.baseUrl, "");
    const dataToSign = `${method}${timestamp}${urlPath}${JSON.stringify(requestBody)}`;
    console.log("Data to sign:", dataToSign);
    console.log("Sign key:", this.config.signKey);
    console.log("Generated signature:", headers["D-SIGNATURE"]);
    console.log("===============================\n");
    
    const config = {
      method,
      url,
      headers,
      data: requestBody,
    };
    try {
      console.log(
        "Sending query history request:",
        JSON.stringify(requestBody, null, 2)
      );
      const response = await axios(config);
      return response.data;
    } catch (error) {
      console.error("Query history error:", error.message);
      if (error.response) {
        console.error("Response status:", error.response.status);
        console.error("Response data:", error.response.data);
      }
      throw error;
    }
  }
}

// Example usage
const dtcClient = new DtcPaymentClient({
  baseUrl: "https://api.sbx.dtcpayment.net",
  merchantId: "85250410150000092",
  terminalId: "25060400225",
  signKey: "GTG4g1uOMJZph2qF", // Replace with your actual sign key
});

async function queryPaymentHistory() {
  try {
    const historyResponse = await dtcClient.queryHistory({
      pageSize: 30,
      pageNo: 1,
    });
    console.log("Query history response:", historyResponse.page.records);
  } catch (error) {
    console.error("Error querying payment history:", error);
  }
}

// Uncomment to run the example
queryPaymentHistory();

module.exports = { DtcPaymentClient }; 