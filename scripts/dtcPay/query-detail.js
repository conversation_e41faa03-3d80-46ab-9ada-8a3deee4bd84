const axios = require("axios");
const crypto = require("crypto");

class DtcQueryDetailClient {
  constructor(config) {
    this.config = config;
  }

  /**
   * Generates the HMAC-SHA512 signature required for DTC API authentication
   */
  generateSignature(method, timestamp, url, body) {
    const urlPath = url.replace(this.config.baseUrl, "");
    let dataToSign = `${method}${timestamp}${urlPath}`;
    if (method.toUpperCase() === "POST" && body) {
      dataToSign += JSON.stringify(body);
    }
    const hmac = crypto.createHmac("sha512", this.config.signKey);
    hmac.update(dataToSign);
    return hmac.digest("base64");
  }

  /**
   * Creates the required DTC headers for the request
   */
  createHeaders(method, url, body) {
    const timestamp = Date.now().toString();
    const signature = this.generateSignature(method, timestamp, url, body);
    return {
      "D-MERCHANT-ID": this.config.merchantId,
      "D-TERMINAL-ID": this.config.terminalId,
      "D-TIMESTAMP": timestamp,
      "D-SIGNATURE": signature,
      "Content-Type": "application/json",
    };
  }

  /**
   * Query payment detail from DTC
   * @param {Object} queryInfo - Query details (transactionId, referenceNo)
   * @returns {Promise<Object>} - Response from DTC
   */
  async queryPaymentDetail(queryInfo) {
    const url = `${this.config.baseUrl}/api/v1/query-detail`;
    const method = "POST";
    const requestBody = {
      transactionId: queryInfo.transactionId,
      referenceNo: queryInfo.referenceNo,
    };
    const config = {
      method,
      url,
      headers: this.createHeaders(method, url, requestBody),
      data: requestBody,
    };
    try {
      console.log(
        "Sending query detail request:",
        JSON.stringify(requestBody, null, 2)
      );
      const response = await axios(config);
      return response.data;
    } catch (error) {
      console.error("Query detail error:", error.message);
      if (error.response) {
        console.error("Response status:", error.response.status);
        console.error("Response data:", error.response.data);
      }
      throw error;
    }
  }
}

// Example usage
const dtcQueryClient = new DtcQueryDetailClient({
  baseUrl: "https://api.sbx.dtcpayment.net",
  merchantId: "85250410150000092",
  terminalId: "25060400225",
  signKey: "GTG4g1uOMJZph2qF", // Replace with your actual sign key
});

// Example query details
async function queryDetailExample() {
  try {
    const queryResponse = await dtcQueryClient.queryPaymentDetail({
      transactionId: "220817101245678",
      referenceNo: "1821938217",
    });
    console.log("Query detail response:", queryResponse);
  } catch (error) {
    console.error("Error querying payment detail:", error);
  }
}

// Run the example
queryDetailExample();

module.exports = { DtcQueryDetailClient }; 