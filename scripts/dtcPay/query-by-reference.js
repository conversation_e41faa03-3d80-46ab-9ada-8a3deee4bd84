const axios = require("axios");
const crypto = require("crypto");

class DtcReferenceQueryClient {
  constructor(config) {
    this.config = config;
  }

  /**
   * Generates the HMAC-SHA512 signature required for DTC API authentication
   */
  generateSignature(method, timestamp, url, body) {
    const urlPath = url.replace(this.config.baseUrl, "");
    let dataToSign = `${method}${timestamp}${urlPath}`;
    if (method.toUpperCase() === "POST" && body) {
      dataToSign += JSON.stringify(body);
    }
    const hmac = crypto.createHmac("sha512", this.config.signKey);
    hmac.update(dataToSign);
    return hmac.digest("base64");
  }

  /**
   * Creates the required DTC headers for the request
   */
  createHeaders(method, url, body) {
    const timestamp = Date.now().toString();
    const signature = this.generateSignature(method, timestamp, url, body);
    return {
      "D-MERCHANT-ID": this.config.merchantId,
      "D-TERMINAL-ID": this.config.terminalId,
      "D-TIMESTAMP": timestamp,
      "D-SIGNATURE": signature,
      "Content-Type": "application/json",
    };
  }

  /**
   * Query payment detail by reference number only
   * @param {string} referenceNo - The unique reference number to locate a transaction
   * @returns {Promise<Object>} - Response from DTC with payment details
   */
  async queryByReference(referenceNo) {
    const url = `${this.config.baseUrl}/api/v1/query-detail`;
    const method = "POST";
    const requestBody = {
      referenceNo: referenceNo,
    };
    
    const headers = this.createHeaders(method, url, requestBody);
    
    // Log request details for debugging
    console.log("=== DTC QUERY BY REFERENCE ===");
    console.log("Method:", method);
    console.log("URL:", url);
    console.log("Reference Number:", referenceNo);
    console.log("\nHeaders:");
    Object.entries(headers).forEach(([key, value]) => {
      console.log(`${key}: ${value}`);
    });
    console.log("\nRequest Body:");
    console.log(JSON.stringify(requestBody, null, 2));
    
    // Show signature generation details
    console.log("\n=== SIGNATURE DEBUG INFO ===");
    const timestamp = headers["D-TIMESTAMP"];
    const urlPath = url.replace(this.config.baseUrl, "");
    const dataToSign = `${method}${timestamp}${urlPath}${JSON.stringify(requestBody)}`;
    console.log("Data to sign:", dataToSign);
    console.log("Sign key:", this.config.signKey);
    console.log("Generated signature:", headers["D-SIGNATURE"]);
    console.log("==============================\n");
    
    const config = {
      method,
      url,
      headers,
      data: requestBody,
    };

    try {
      console.log(`Querying payment detail for reference: ${referenceNo}`);
      const response = await axios(config);
      
      console.log("=== RESPONSE RECEIVED ===");
      console.log("Status:", response.status);
      console.log("Data:", JSON.stringify(response.data, null, 2));

      // Check for DTC API-level errors
      if (response.data.header && response.data.header.code !== 200) {
        const errorCode = response.data.header.code;
        let errorMessage = "Unknown DTC API error";

        switch (errorCode) {
          case 400:
            errorMessage = "Bad Request - Reference number not found or invalid";
            break;
          case 401:
            errorMessage = "Unauthorized - Invalid credentials or signature";
            break;
          case 404:
            errorMessage = "Not Found - Transaction not found";
            break;
          case 500:
            errorMessage = "Internal Server Error";
            break;
        }

        console.log(`\n❌ DTC API Error ${errorCode}: ${errorMessage}`);
        throw new Error(`DTC API Error ${errorCode}: ${errorMessage}`);
      }

      return response.data;
    } catch (error) {
      console.error("Query by reference error:", error.message);
      if (error.response) {
        console.error("Response status:", error.response.status);
        console.error("Response headers:", error.response.headers);
        console.error("Response data:", JSON.stringify(error.response.data, null, 2));
      }
      throw error;
    }
  }

  /**
   * Helper method to extract key payment information from response
   */
  extractPaymentInfo(response) {
    if (!response) return null;
    
    return {
      transactionId: response.transactionId || "N/A",
      referenceNo: response.referenceNo || "N/A",
      merchantId: response.merchantId || "N/A",
      merchantName: response.merchantName || "N/A",
      amount: response.totalAmount || response.processingAmount || "N/A",
      currency: response.processingCurrency || "N/A",
      status: response.settlementStatus || "N/A",
      state: response.state || "N/A",
      terminalId: response.terminalId || "N/A",
      serviceAmount: response.serviceAmount || "N/A",
      tipAmount: response.tipAmount || "N/A",
    };
  }
}

// Configuration for DTC sandbox environment
const dtcClient = new DtcReferenceQueryClient({
  baseUrl: "https://api.sbx.dtcpayment.net",
  merchantId: "85250410150000092",
  terminalId: "25060400225",
  signKey: "GTG4g1uOMJZph2qF", // Replace with your actual sign key
});

/**
 * Main function to query payment by reference number
 */
async function queryPaymentByReference(referenceNumber) {
  try {
    const response = await dtcClient.queryByReference(referenceNumber);
    
    console.log("\n=== PAYMENT DETAILS ===");
    const paymentInfo = dtcClient.extractPaymentInfo(response);
    console.log("Extracted Payment Info:");
    console.log(JSON.stringify(paymentInfo, null, 2));
    
    return response;
  } catch (error) {
    console.error("Failed to query payment:", error.message);
    return null;
  }
}

/**
 * Example usage with a sample reference number
 */
async function runExample() {
  // Example reference number from the API documentation
  const sampleReferenceNumber = "1821938217";
  
  console.log(`Querying payment details for reference: ${sampleReferenceNumber}`);
  await queryPaymentByReference(sampleReferenceNumber);
}

// Uncomment to run the example
// runExample();

// Export for use in other modules
module.exports = { 
  DtcReferenceQueryClient,
  queryPaymentByReference 
};

// If this file is run directly, execute the example
if (require.main === module) {
  const referenceNumber = process.argv[2] || "1821938217";
  console.log(`Running query for reference number: ${referenceNumber}`);
  queryPaymentByReference(referenceNumber);
}
