const axios = require('axios');
const crypto = require('crypto');

class DtcApiClient {
  constructor(config) {
    this.config = config;
  }

  /**
   * Generates the HMAC-SHA512 signature required for DTC API authentication
   */
  generateSignature(method, timestamp, url, body) {
    // Extract relative path from the full URL if needed
    const urlPath = url.replace(this.config.baseUrl, '');
    
    // Create the string to sign based on the rules:
    // Request Method + D-TIMESTAMP + Request Url + urlParameter/requestBody
    let dataToSign = `${method}${timestamp}${urlPath}`;
    
    // Add request body for POST requests
    if (method.toUpperCase() === 'POST' && body) {
      dataToSign += JSON.stringify(body);
    }
    
    // Generate HMAC-SHA512 signature
    const hmac = crypto.createHmac('sha512', this.config.signKey);
    hmac.update(dataToSign);
    
    // Return Base64 encoded signature
    return hmac.digest('base64');
  }

  /**
   * Creates the required DTC headers for the request
   */
  createHeaders(method, url, body) {
    // Generate SGT timestamp in milliseconds
    const timestamp = Date.now().toString();
    
    // Generate signature
    const signature = this.generateSignature(method, timestamp, url, body);
    
    // Return headers
    return {
      'D-MERCHANT-ID': this.config.merchantId,
      'D-TERMINAL-ID': this.config.terminalId,
      'D-TIMESTAMP': timestamp,
      'D-SIGNATURE': signature,
      'Content-Type': 'application/json'
    };
  }

  /**
   * Sign in to the DTC API
   */
  async signIn() {
    const url = `${this.config.baseUrl}/api/v1/sign-in`;
    const method = 'GET';
    
    const config = {
      method,
      url,
      headers: this.createHeaders(method, url)
    };
    
    try {
      const response = await axios(config);
      return response.data;
    } catch (error) {
      console.error('Sign-in error:', error);
      throw error;
    }
  }

  /**
   * Generic method to make any API request to DTC
   */
  async request(method, endpoint, data) {
    const url = `${this.config.baseUrl}${endpoint}`;
    
    const config = {
      method,
      url,
      headers: this.createHeaders(method, url, data),
      data: method.toUpperCase() !== 'GET' ? data : undefined
    };
    
    try {
      const response = await axios(config);
      return response.data;
    } catch (error) {
      console.error(`API request error (${method} ${endpoint}):`, error);
      throw error;
    }
  }
}

// Example usage
const dtcClient = new DtcApiClient({
  baseUrl: 'https://api.sbx.dtcpayment.net',
  merchantId: '85250410150000092', // Assigned by DTC
  terminalId: '25052000192', // Assigned by DTC
  signKey: 'L04b2kelLnGMyBi8' // Assigned by DTC
});

// Example implementation for sign-in endpoint
async function example() {
  try {
    const signInResponse = await dtcClient.signIn();
    console.log('Sign-in successful:', signInResponse);
    
    // Additional API calls can be made using the generic request method
    // For example:
    // const someData = await dtcClient.request('POST', '/api/v1/some-endpoint', { key: 'value' });
    
  } catch (error) {
    console.error('Error:', error);
  }
}

example();

module.exports = { DtcApiClient };