const axios = require("axios");
const crypto = require("crypto");

class DtcPaymentClient {
  constructor(config) {
    this.config = config;
  }

  /**
   * Generates the HMAC-SHA512 signature required for DTC API authentication
   */
  generateSignature(method, timestamp, url, body) {
    // Extract relative path from the full URL if needed
    const urlPath = url.replace(this.config.baseUrl, "");

    // Create the string to sign based on the rules
    let dataToSign = `${method}${timestamp}${urlPath}`;

    // Add request body for POST requests
    if (method.toUpperCase() === "POST" && body) {
      dataToSign += JSON.stringify(body);
    }

    // Generate HMAC-SHA512 signature
    const hmac = crypto.createHmac("sha512", this.config.signKey);
    hmac.update(dataToSign);

    // Return Base64 encoded signature
    return hmac.digest("base64");
  }

  /**
   * Creates the required DTC headers for the request
   */
  createHeaders(method, url, body) {
    // Generate SGT timestamp in milliseconds
    const timestamp = Date.now().toString();

    // Generate signature
    const signature = this.generateSignature(method, timestamp, url, body);

    // Return headers
    return {
      "D-MERCHANT-ID": this.config.merchantId,
      "D-TERMINAL-ID": this.config.terminalId,
      "D-TIMESTAMP": timestamp,
      "D-SIGNATURE": signature,
      "Content-Type": "application/json",
    };
  }

  /**
   * Get payment page from DTC
   * @param {Object} paymentInfo - Payment details
   * @returns {Promise<Object>} - Response from DTC
   */
  async getPaymentPage(paymentInfo) {
    const url = `${this.config.baseUrl}/api/v1/payment-page`;
    const method = "POST";

    // Create request body according to API documentation
    const requestBody = {
      transaction: {
        totalAmount: paymentInfo.totalAmount,
        saleAmount: paymentInfo.saleAmount,
        serviceFeeAmount: paymentInfo.serviceFeeAmount,
        gstAmount: paymentInfo.gstAmount,
        tipAmount: paymentInfo.tipAmount,
        tipCurrency: paymentInfo.tipCurrency,
        serviceFeeCurrency: paymentInfo.serviceFeeCurrency,
        gstCurrency: paymentInfo.gstCurrency,
        referenceNo: paymentInfo.referenceNo,
        processingCurrency: paymentInfo.processingCurrency,
        notificationUrl: paymentInfo.notificationUrl || null,
        redirectUrl: paymentInfo.redirectUrl || null,
        billingInfo: paymentInfo.billingInfo || {
          firstName: paymentInfo.firstName,
          lastName: paymentInfo.lastName,
          email: paymentInfo.email,
          phone: paymentInfo.phone || null,
          country: paymentInfo.country,
          state: paymentInfo.state || null,
          city: paymentInfo.city || null,
          postcode: paymentInfo.postcode,
          address: paymentInfo.address,
          address2: paymentInfo.address2 || null,
        },
        shippingInfo: paymentInfo.shippingInfo || null,
      }
    };

    // Create request config
    const config = {
      method,
      url,
      headers: this.createHeaders(method, url, requestBody),
      data: requestBody,
    };

    try {
      console.log(
        "Sending payment page request:",
        JSON.stringify(requestBody, null, 2)
      );
      const response = await axios(config);
      return response.data;
    } catch (error) {
      console.error("Payment page error:", error.message);
      if (error.response) {
        console.error("Response status:", error.response.status);
        console.error("Response data:", error.response.data);
      }
      throw error;
    }
  }
}

// Example usage
const dtcClient = new DtcPaymentClient({
  baseUrl: "https://api.dtcpayment.net", // Updated to match the image
  merchantId: "8525030590",
  terminalId: "25060400225",
  signKey: "aVfWEMAdT1ZxWeDX", // Replace with your actual sign key
});

// Example transaction details
async function createPaymentPage() {
  try {
    const paymentResponse = await dtcClient.getPaymentPage({
      // Required transaction fields
      totalAmount: "400.00",
      saleAmount: "100.00",
      serviceFeeAmount: "100.00",
      gstAmount: "100.00",
      tipAmount: "100.00",
      tipCurrency: "USD",
      serviceFeeCurrency: "USD",
      processingCurrency: "USD",
      gstCurrency: "USD",
      referenceNo: "1981938237",
      notificationUrl: "https://suntory.ekoios.net/api/dtcpay-webhooks",
      redirectUrl: "https://www.instagram.com/",
      
      billingInfo: {
        firstName: "AAA",
        lastName: "BBB",
        email: "<EMAIL>",
        phone: "12345678",
        country: "Singapore",
        state: "Singapore",
        city: "Singapore",
        postcode: "123456",
        address: "Billing Line 1",
        address2: "Billing Line 2",
      },

      shippingInfo: {
        firstName: "CCC",
        lastName: "DDD",
        email: "<EMAIL>",
        phone: "12345678",
        country: "Singapore",
        state: "Singapore",
        city: "Singapore",
        postcode: "654321",
        address: "Shipping Line 1",
        address2: "Shipping Line 2",
      },
    });

    console.log("Payment page response:", paymentResponse);

    // If successful, paymentResponse should contain a payment URL to redirect the customer to
    if (
      paymentResponse &&
      paymentResponse.header &&
      paymentResponse.header.success
    ) {
      console.log("Redirect customer to:", paymentResponse.paymentPageUrl);
    }
  } catch (error) {
    console.error("Error creating payment page:", error);
  }
}

// Run the example
createPaymentPage();

module.exports = { DtcPaymentClient };