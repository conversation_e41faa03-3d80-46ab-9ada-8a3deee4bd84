const { ethers } = require("ethers");
const { hashNftBuyRequest, createNftBuyRequest } = require('./nftBuyRequestHash');

/**
 * Creates and signs an NftBuyRequest for use with the buyNowNative function
 */
class NftBuyRequestSigner {
  constructor(signer) {
    if (!signer) {
      throw new Error('Signer is required');
    }
    this.signer = signer;
  }

  /**
   * Creates a hash for the NftBuyRequest (equivalent to the Solidity hash function)
   * @param {Object} buyRequest - The NftBuyRequest object
   * @returns {string} - The hash as hex string
   */
  hash(buyRequest) {
    return hashNftBuyRequest(buyRequest);
  }

  /**
   * Signs an NftBuyRequest hash using Ethereum signed message format
   * @param {Object} buyRequest - The NftBuyRequest object
   * @returns {Promise<string>} - The signature as hex string
   */
  async signBuyRequest(buyRequest) {
    const hash = this.hash(buyRequest);
    
    // Create Ethereum signed message hash (equivalent to MessageHashUtils.toEthSignedMessageHash)
    const ethSignedMessageHash = ethers.utils.hashMessage(ethers.utils.arrayify(hash));
    
    // Sign the hash
    const signature = await this.signer.signMessage(ethers.utils.arrayify(hash));
    
    return signature;
  }

  /**
   * Verifies a signature against an NftBuyRequest
   * @param {Object} buyRequest - The NftBuyRequest object
   * @param {string} signature - The signature to verify
   * @param {string} expectedSigner - The expected signer address
   * @returns {boolean} - True if signature is valid
   */
  verifySignature(buyRequest, signature, expectedSigner) {
    try {
      const hash = this.hash(buyRequest);
      const recoveredAddress = ethers.utils.verifyMessage(ethers.utils.arrayify(hash), signature);
      return recoveredAddress.toLowerCase() === expectedSigner.toLowerCase();
    } catch (error) {
      console.error('Signature verification error:', error);
      return false;
    }
  }

  /**
   * Creates a complete signed buy request ready for the buyNowNative function
   * @param {Array} data - The data array for buyNowNative
   * @param {Array} addresses - The addresses array for buyNowNative  
   * @param {string} sellOrderSignature - The sell order signature
   * @returns {Promise<Object>} - Object containing buyRequest, hash, and signature
   */
  async createSignedBuyRequest(data, addresses, sellOrderSignature) {
    // Create the buy request object
    const buyRequest = createNftBuyRequest(data, addresses, sellOrderSignature);
    
    // Calculate hash
    const hash = this.hash(buyRequest);
    
    // Create signature
    const signature = await this.signBuyRequest(buyRequest);
    
    return {
      buyRequest,
      hash,
      signature,
      // Helper method to get the data needed for contract call
      getContractCallData: () => ({
        data: data,
        addresses: addresses,
        signatures: [signature, sellOrderSignature]
      })
    };
  }

  /**
   * Helper method to create data array from individual parameters
   * @param {Object} params - Parameters object
   * @returns {Array} - Data array for buyNowNative
   */
  static createDataArray(params) {
    const {
      tokenID,
      quantity,
      sellOrderSupply,
      sellOrderPrice,
      enableMakeOffer,
      buyingAmount,
      tokenType,
      partnerType,
      partnerFee,
      transactionType,
      storeFeeRatio,
      shippingFee,
      expiredAt
    } = params;

    return [
      tokenID,
      quantity,
      sellOrderSupply,
      sellOrderPrice,
      enableMakeOffer,
      buyingAmount,
      tokenType,
      partnerType,
      partnerFee,
      transactionType,
      storeFeeRatio,
      shippingFee,
      expiredAt
    ];
  }

  /**
   * Helper method to create addresses array from individual parameters
   * @param {Object} params - Parameters object
   * @returns {Array} - Addresses array for buyNowNative
   */
  static createAddressesArray(params) {
    const {
      creator,
      tokenAddress,
      contractAddress,
      signer,
      storeAddress,
      receiver
    } = params;

    return [
      creator,
      tokenAddress,
      contractAddress,
      signer,
      storeAddress,
      receiver
    ];
  }
}

/**
 * Convenience function to create a signer instance
 * @param {string|ethers.Wallet|ethers.Signer} signerOrPrivateKey - Private key string or signer instance
 * @returns {NftBuyRequestSigner} - Signer instance
 */
function createSigner(signerOrPrivateKey) {
  let signer;
  
  if (typeof signerOrPrivateKey === 'string') {
    // Assume it's a private key
    signer = new ethers.Wallet(signerOrPrivateKey);
  } else if (signerOrPrivateKey.signMessage) {
    // Assume it's already a signer
    signer = signerOrPrivateKey;
  } else {
    throw new Error('Invalid signer or private key provided');
  }
  
  return new NftBuyRequestSigner(signer);
}

module.exports = {
  NftBuyRequestSigner,
  createSigner
};
