// scripts/helpers.js
const hre = require("hardhat");
const {
  BUY_HANDLER,
  SELL_HANDLER,
  OFFER_HANDLER,
  AIRDROP_HANDLER,
  EXCHANGE,
  PROXY,
  META_HANDLER,
  NFTIFY721,
  NFTIFY1155,
  RINKEBY,
  BSCTESTNET,
  MUMBAI,
  FEE_UTILS,
  BOX_UTILS,
  CANCEL_HANDLER,
} = require("./constants");
require("dotenv").config();

const detectContract = (contract) => {
  switch (contract) {
    case BUY_HANDLER:
      return "handlers/BuyHandler.sol:BuyHandler";
    case SELL_HANDLER:
      return "handlers/SellHandler.sol:SellHandler";
    case OFFER_HANDLER:
      return "handlers/OfferHandler.sol:OfferHandler";
    case AIRDROP_HANDLER:
      return "handlers/AirdropHandler.sol:AirdropHandler";
    case CANCEL_HANDLER:
      return "handlers/CancelHandler.sol:CancelHandler";
    case EXCHANGE:
      return "exchanges/NFTExchangeV1.sol:NFTExchangeV1";
    case PROXY:
      return "NFTExchangeProxy.sol:NFTExchangeProxy";
    case META_HANDLER:
      return "handlers/MetaHandler.sol:MetaHandler";
    case NFTIFY721:
      return "collections/NFTify721.sol:NFTify721";
    case NFTIFY1155:
      return "collections/NFTify1155.sol:NFTify1155";
    case FEE_UTILS:
      return "utils/FeeUtils.sol:FeeUtils";
    case BOX_UTILS:
      return "handlers/OpenBox.sol:OpenBox";
  }
};

const detectNetwork = async () => {
  const chainId = (await hre.ethers.provider.getNetwork()).chainId;

  switch (chainId) {
    case RINKEBY:
      return "ethereum";
    case BSCTESTNET:
      return "bsc";
    case MUMBAI:
      return "polygon";
  }
};

const loadContractFactory = async (contract) => {
  const _network = await detectNetwork();
  const _contract = detectContract(contract);

  if ([NFTIFY1155, NFTIFY721, PROXY].includes(contract))
    return await hre.ethers.getContractFactory(`contracts/${_contract}`);

  return await hre.ethers.getContractFactory(
    `contracts/${_network}/${_contract}`
  );
};

// Function for loading a proxy contract
const loadProxyContract = async () => {
  const chainId = (await hre.ethers.provider.getNetwork()).chainId;

  switch (chainId) {
    case RINKEBY:
      return loadContract(EXCHANGE, process.env.ETH_PROXY);
    case BSCTESTNET:
      return loadContract(EXCHANGE, process.env.BSC_PROXY);
    case MUMBAI:
      return loadContract(EXCHANGE, process.env.POLYGON_PROXY);
  }
};

// Function for loading an existing contract from a specific address
const loadContract = async (contract, address) =>
  (await loadContractFactory(contract)).attach(address);

const deployContract = async (_contract, _params = []) => {
  console.log(_params);
  console.log("--------------");
  const _provider = (await hre.ethers.provider.getNetwork()).chainId;
  console.log("Current network: ", _provider);

  const [deploy_wallet] = await hre.ethers.getSigners();
  console.log("Deploy wallet: ", deploy_wallet.address);

  const CONTRACT_FACTORY = await loadContractFactory(_contract);
  let contract;
  if (_params.length == 0) {
    contract = await CONTRACT_FACTORY.deploy();
  } else {
    contract = await CONTRACT_FACTORY.deploy(..._params);
  }

  console.log("hreer");
  await contract.deployed();

  console.log(`Contract for ${_contract} deployed: `, contract.address);

  return contract;
};

const hash = (_object) =>
  hre.ethers.utils.solidityKeccak256(
    Object.values(_object.type),
    Object.values(_object.value)
  );

const createSignature = async (_object) =>
  await hre.ethers.provider
    .getSigner()
    .signMessage(hre.ethers.utils.arrayify(hash(_object)));

const genRanHex = (size) =>
  [...Array(size)]
    .map(() => Math.floor(Math.random() * 16).toString(16))
    .join("")
    .toString();

module.exports = {
  deployContract,
  loadContract,
  createSignature,
  genRanHex,
  loadProxyContract,
};
