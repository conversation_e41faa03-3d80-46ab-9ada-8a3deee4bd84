const { ethers } = require("ethers");

/**
 * JavaScript implementation of the NftBuyRequest hash function
 * This replicates the exact logic from BuyHandler.sol
 */

/**
 * Pads a hex string to ensure it's the correct length
 * @param {string} hex - The hex string to pad
 * @param {number} length - The desired length in bytes
 * @returns {string} - The padded hex string
 */
function padHex(hex, length) {
  // Remove '0x' prefix if present
  const cleanHex = hex.startsWith('0x') ? hex.slice(2) : hex;
  // Pad with zeros to the left
  return cleanHex.padStart(length * 2, '0');
}

/**
 * Converts a uint256 to a 32-byte hex string (equivalent to AssemblyUtils.writeUint256)
 * @param {string|number|BigInt} value - The value to convert
 * @returns {string} - 32-byte hex string without '0x' prefix
 */
function writeUint256(value) {
  const bn = ethers.BigNumber.from(value);
  return padHex(bn.toHexString(), 32);
}

/**
 * Converts an address to a 20-byte hex string (equivalent to AssemblyUtils.writeAddress)
 * @param {string} address - The address to convert
 * @returns {string} - 20-byte hex string without '0x' prefix
 */
function writeAddress(address) {
  // Ensure address is valid and remove '0x' prefix
  const cleanAddress = ethers.utils.getAddress(address).slice(2);
  return cleanAddress.toLowerCase();
}

/**
 * Converts bytes to hex string (equivalent to AssemblyUtils.writeBytes)
 * @param {string} bytes - The bytes to convert (hex string with or without '0x')
 * @returns {string} - Hex string without '0x' prefix
 */
function writeBytes(bytes) {
  return bytes.startsWith('0x') ? bytes.slice(2) : bytes;
}

/**
 * Hash function for NftBuyRequest - equivalent to the Solidity hash function
 * @param {Object} buyRequest - The NftBuyRequest object
 * @param {string|number|BigInt} buyRequest.tokenID
 * @param {string|number|BigInt} buyRequest.quantity
 * @param {string|number|BigInt} buyRequest.sellOrderSupply
 * @param {string|number|BigInt} buyRequest.sellOrderPrice
 * @param {string|number|BigInt} buyRequest.enableMakeOffer
 * @param {string|number|BigInt} buyRequest.tokenType
 * @param {string|number|BigInt} buyRequest.partnerType
 * @param {string|number|BigInt} buyRequest.partnerFee
 * @param {string|number|BigInt} buyRequest.transactionType
 * @param {string|number|BigInt} buyRequest.shippingFee
 * @param {string|number|BigInt} buyRequest.expiredAt
 * @param {string} buyRequest.creator - Address
 * @param {string} buyRequest.receiver - Address
 * @param {string} buyRequest.tokenAddress - Address
 * @param {string} buyRequest.contractAddress - Address
 * @param {string} buyRequest.sellOrderSignature - Hex string
 * @returns {string} - The keccak256 hash as hex string with '0x' prefix
 */
function hashNftBuyRequest(buyRequest) {
  // Validate required fields
  const requiredFields = [
    'tokenID', 'quantity', 'sellOrderSupply', 'sellOrderPrice', 'enableMakeOffer',
    'tokenType', 'partnerType', 'partnerFee', 'transactionType', 'shippingFee', 'expiredAt',
    'creator', 'receiver', 'tokenAddress', 'contractAddress', 'sellOrderSignature'
  ];
  
  for (const field of requiredFields) {
    if (buyRequest[field] === undefined || buyRequest[field] === null) {
      throw new Error(`Missing required field: ${field}`);
    }
  }

  // Build the data array exactly as in Solidity
  let dataHex = '';
  
  // Write all uint256 values (11 fields, 32 bytes each = 0x20 * 11)
  dataHex += writeUint256(buyRequest.tokenID);
  dataHex += writeUint256(buyRequest.quantity);
  dataHex += writeUint256(buyRequest.sellOrderSupply);
  dataHex += writeUint256(buyRequest.sellOrderPrice);
  dataHex += writeUint256(buyRequest.enableMakeOffer);
  dataHex += writeUint256(buyRequest.tokenType);
  dataHex += writeUint256(buyRequest.partnerType);
  dataHex += writeUint256(buyRequest.partnerFee);
  dataHex += writeUint256(buyRequest.transactionType);
  dataHex += writeUint256(buyRequest.shippingFee);
  dataHex += writeUint256(buyRequest.expiredAt);
  
  // Write all addresses (4 fields, 20 bytes each = 0x14 * 4)
  dataHex += writeAddress(buyRequest.creator);
  dataHex += writeAddress(buyRequest.receiver);
  dataHex += writeAddress(buyRequest.tokenAddress);
  dataHex += writeAddress(buyRequest.contractAddress);
  
  // Write the signature bytes
  dataHex += writeBytes(buyRequest.sellOrderSignature);
  
  // Calculate keccak256 hash
  const hash = ethers.utils.keccak256('0x' + dataHex);
  
  return hash;
}

/**
 * Helper function to create a properly formatted NftBuyRequest object
 * @param {Array} data - Array of uint256 values [tokenID, quantity, sellOrderSupply, ...]
 * @param {Array} addresses - Array of addresses [creator, tokenAddress, contractAddress, receiver]
 * @param {string} sellOrderSignature - The sell order signature
 * @returns {Object} - Formatted NftBuyRequest object
 */
function createNftBuyRequest(data, addresses, sellOrderSignature) {
  if (data.length < 13) {
    throw new Error('Data array must have at least 13 elements including expiredAt');
  }
  
  if (addresses.length < 4) {
    throw new Error('Addresses array must have at least 4 elements');
  }
  
  return {
    tokenID: data[0],
    quantity: data[1],
    sellOrderSupply: data[2],
    sellOrderPrice: data[3],
    enableMakeOffer: data[4],
    tokenType: data[6],
    partnerType: data[7],
    partnerFee: data[8],
    transactionType: data[9],
    shippingFee: data[11],
    expiredAt: data[12],
    creator: addresses[0],
    receiver: addresses[3],
    tokenAddress: addresses[1],
    contractAddress: addresses[2],
    sellOrderSignature: sellOrderSignature
  };
}

module.exports = {
  hashNftBuyRequest,
  createNftBuyRequest,
  writeUint256,
  writeAddress,
  writeBytes
};
