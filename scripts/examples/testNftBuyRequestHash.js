const { hashNftBuyRequest, createNftBuyRequest } = require('../utils/nftBuyRequestHash');

/**
 * Example usage of the NftBuyRequest hash function
 */
async function testHashFunction() {
  console.log('Testing NftBuyRequest Hash Function\n');
  
  // Example data - similar to what you might use in your application
  const data = [
    "0x7242f542186bddfe3180f102290d38b3552c47bf9f00204dc38814a670366735", // tokenID
    "100",                    // quantity
    "100",                    // sellOrderSupply
    "1000000000000000000",    // sellOrderPrice (1 ETH in wei)
    "1",                      // enableMakeOffer
    "1",                      // buyingAmount (data[5])
    "0",                      // tokenType (ERC1155: 0, ERC721: 1)
    "0",                      // partnerType
    "100",                    // partnerFee
    "0",                      // transactionType
    "0",                      // storeFeeRatio (data[10])
    "0",                      // shippingFee
    Math.floor(Date.now() / 1000) + 3600 // expiredAt (1 hour from now)
  ];
  
  const addresses = [
    "******************************************", // creator
    "******************************************", // tokenAddress
    "******************************************", // contractAddress
    "******************************************"  // receiver
  ];
  
  const sellOrderSignature = "0x0d29eba40db65378be0888e46eafdb1803347a015e083fa8910693d9bafa96474520ddfd713577633c616319dee3e29fd093fca5b9132e8324c1a0c6b7d474511c";
  
  try {
    // Method 1: Create NftBuyRequest object manually
    const buyRequest1 = {
      tokenID: data[0],
      quantity: data[1],
      sellOrderSupply: data[2],
      sellOrderPrice: data[3],
      enableMakeOffer: data[4],
      tokenType: data[6],
      partnerType: data[7],
      partnerFee: data[8],
      transactionType: data[9],
      shippingFee: data[11],
      expiredAt: data[12],
      creator: addresses[0],
      receiver: addresses[3],
      tokenAddress: addresses[1],
      contractAddress: addresses[2],
      sellOrderSignature: sellOrderSignature
    };
    
    const hash1 = hashNftBuyRequest(buyRequest1);
    console.log('Method 1 - Manual object creation:');
    console.log('Hash:', hash1);
    console.log('');
    
    // Method 2: Use helper function
    const buyRequest2 = createNftBuyRequest(data, addresses, sellOrderSignature);
    const hash2 = hashNftBuyRequest(buyRequest2);
    
    console.log('Method 2 - Using helper function:');
    console.log('Hash:', hash2);
    console.log('');
    
    // Verify both methods produce the same hash
    console.log('Hashes match:', hash1 === hash2);
    console.log('');
    
    // Display the buyRequest object structure
    console.log('NftBuyRequest object structure:');
    console.log(JSON.stringify(buyRequest2, null, 2));
    
  } catch (error) {
    console.error('Error:', error.message);
  }
}

/**
 * Example of how to use this with your existing test data
 */
async function testWithExistingData() {
  console.log('\n--- Testing with existing test data format ---\n');
  
  // This matches the format from your test/data/buy_handler.js
  const testData = [
    "0x7242f542186bddfe3180f102290d38b3552c47bf9f00204dc38814a670366735",
    "100",
    "100", 
    "1000000000000000000",
    "1",
    1,     // buyingAmount
    0,     // tokenType
    0,     // partnerType
    "100", // partnerFee
    0,     // transactionType
    0,     // storeFeeRatio
    0,     // shippingFee
    Math.floor(Date.now() / 1000) + 3600 // expiredAt
  ];
  
  const testAddresses = [
    "******************************************", // creator
    "******************************************", // tokenAddress
    "******************************************", // contractAddress
    "0xc38470D323a77A70AA0dCcaB97D29965cc67D88E", // signer
    "******************************************", // storeAddress
    "******************************************"  // receiver
  ];
  
  const testSignature = "0x0d29eba40db65378be0888e46eafdb1803347a015e083fa8910693d9bafa96474520ddfd713577633c616319dee3e29fd093fca5b9132e8324c1a0c6b7d474511c";
  
  try {
    // Create the request using the helper function
    const buyRequest = createNftBuyRequest(testData, testAddresses, testSignature);
    const hash = hashNftBuyRequest(buyRequest);
    
    console.log('Test Data Hash:', hash);
    console.log('');
    console.log('This hash should match what you get from the Solidity contract');
    console.log('when called with the same parameters.');
    
  } catch (error) {
    console.error('Error:', error.message);
  }
}

// Run the tests
if (require.main === module) {
  testHashFunction().then(() => {
    testWithExistingData();
  });
}

module.exports = {
  testHashFunction,
  testWithExistingData
};
