const { ethers } = require("ethers");
const { createSigner, NftBuyRequestSigner } = require('../utils/nftBuyRequestSigner');

/**
 * Complete example of how to create and sign an NftBuyRequest for buyNowNative
 */
async function buyNowNativeExample() {
  console.log('=== BuyNowNative Complete Example ===\n');
  
  // Example private key (DO NOT use this in production!)
  const privateKey = "0xa3911655f6890a4ece9cb18f5c394bff1fd2cf7f10e9b874f434cd5e68a03ffe";
  
  // Create signer
  const signer = createSigner(privateKey);
  console.log('Signer address:', await signer.signer.getAddress());
  console.log('');
  
  // Define the parameters for the buy request
  const buyParams = {
    tokenID: "0x7242f542186bddfe3180f102290d38b3552c47bf9f00204dc38814a670366735",
    quantity: "100",
    sellOrderSupply: "100",
    sellOrderPrice: "1000000000000000000", // 1 ETH in wei
    enableMakeOffer: "1",
    buyingAmount: "1",
    tokenType: "0", // ERC1155
    partnerType: "0",
    partnerFee: "100",
    transactionType: "0",
    storeFeeRatio: "0",
    shippingFee: "0",
    expiredAt: (Math.floor(Date.now() / 1000) + 3600).toString() // 1 hour from now
  };
  
  const addressParams = {
    creator: "******************************************",
    tokenAddress: "******************************************",
    contractAddress: "******************************************",
    signer: await signer.signer.getAddress(),
    storeAddress: "******************************************",
    receiver: "******************************************"
  };
  
  // Sell order signature (this would come from your backend/database)
  const sellOrderSignature = "0x0d29eba40db65378be0888e46eafdb1803347a015e083fa8910693d9bafa96474520ddfd713577633c616319dee3e29fd093fca5b9132e8324c1a0c6b7d474511c";
  
  try {
    // Create data arrays
    const data = NftBuyRequestSigner.createDataArray(buyParams);
    const addresses = NftBuyRequestSigner.createAddressesArray(addressParams);
    
    console.log('Data array:', data);
    console.log('Addresses array:', addresses);
    console.log('');
    
    // Create signed buy request
    const signedRequest = await signer.createSignedBuyRequest(data, addresses, sellOrderSignature);
    
    console.log('=== Generated Results ===');
    console.log('Hash:', signedRequest.hash);
    console.log('Signature:', signedRequest.signature);
    console.log('');
    
    // Verify the signature
    const isValid = signer.verifySignature(
      signedRequest.buyRequest, 
      signedRequest.signature, 
      await signer.signer.getAddress()
    );
    console.log('Signature verification:', isValid ? 'VALID' : 'INVALID');
    console.log('');
    
    // Get contract call data
    const contractCallData = signedRequest.getContractCallData();
    console.log('=== Contract Call Data ===');
    console.log('Data for buyNowNative:');
    console.log('  data:', contractCallData.data);
    console.log('  addresses:', contractCallData.addresses);
    console.log('  strs: ["your-internal-tx-id"]');
    console.log('  signatures:', contractCallData.signatures);
    console.log('');
    
    // Show how to call the contract (pseudo-code)
    console.log('=== Contract Call Example ===');
    console.log('// Example of how to call buyNowNative with this data:');
    console.log('const tx = await nftExchange.buyNowNative(');
    console.log('  contractCallData.data,');
    console.log('  contractCallData.addresses,');
    console.log('  ["your-internal-tx-id"],');
    console.log('  contractCallData.signatures,');
    console.log('  { value: ethers.utils.parseEther("0.001") } // if paying with ETH');
    console.log(');');
    
  } catch (error) {
    console.error('Error:', error.message);
  }
}

/**
 * Example showing how to work with existing test data format
 */
async function testDataFormatExample() {
  console.log('\n=== Working with Existing Test Data Format ===\n');
  
  const privateKey = "0xa3911655f6890a4ece9cb18f5c394bff1fd2cf7f10e9b874f434cd5e68a03ffe";
  const signer = createSigner(privateKey);
  
  // This matches your existing test data format
  const testData = [
    "0x7242f542186bddfe3180f102290d38b3552c47bf9f00204dc38814a670366735",
    "100",
    "100",
    "1000000000000000000",
    "1",
    1,     // buyingAmount
    0,     // tokenType
    0,     // partnerType
    "100", // partnerFee
    0,     // transactionType
    0,     // storeFeeRatio
    0,     // shippingFee
    Math.floor(Date.now() / 1000) + 3600 // expiredAt
  ];
  
  const testAddresses = [
    "******************************************", // creator
    "******************************************", // tokenAddress
    "******************************************", // contractAddress
    await signer.signer.getAddress(),                 // signer
    "******************************************", // storeAddress
    "******************************************"  // receiver
  ];
  
  const sellOrderSignature = "0x0d29eba40db65378be0888e46eafdb1803347a015e083fa8910693d9bafa96474520ddfd713577633c616319dee3e29fd093fca5b9132e8324c1a0c6b7d474511c";
  
  try {
    const signedRequest = await signer.createSignedBuyRequest(testData, testAddresses, sellOrderSignature);
    
    console.log('Hash from test data:', signedRequest.hash);
    console.log('Signature from test data:', signedRequest.signature);
    
    // This signature can now be used in your test files or actual contract calls
    console.log('\nThis signature can be used in your buyNowNative calls!');
    
  } catch (error) {
    console.error('Error:', error.message);
  }
}

// Run examples
if (require.main === module) {
  buyNowNativeExample().then(() => {
    return testDataFormatExample();
  }).catch(console.error);
}

module.exports = {
  buyNowNativeExample,
  testDataFormatExample
};
