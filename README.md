# Platform Deployment: (Ethereum & Polygon)

## NFTify1155.sol:
    -Deploy NftifyExchangeProxy
    -Deploy SignatureUtils
    -Deploy BuyHandler
    -Deploy OfferHandler
    -Deploy Nftify1155(uri, nftifyExchangeProxyAddress)

## NFTify721.sol:
    -Deploy ERC721BuyHandler
    -Deploy ERC721OfferHandler
    -Deploy Nftify721(name, symbol, nftifyExchangeProxyAddress)
    -Deploy NftifyExchangeV1

## Setup address
    -Call upgradeTo(nftifyExchangeV1Address) at NftifyExchangeProxy Contract
    -(OPTIONAL) Call setAdminList(walletAddress) to allow permission for setting addresses // If already has permission then ignore this step
    -Load NftifyExchangeV1 at nftifyExchangeProxyAddress at deployment tab
        +Call setBuyHandlerAddress(buyHandlerAddress)
        +Call setOfferHanderAddress(offerHandlerAddress)
        +Call setSignatureUtilsAddress(signatureUtilsAddress)
        +Call setNftifyAddress(nftify1155Address)
        +Call setERC721BuyHandlerAddress(erc721BuyHandlerAddress)
        +Call setERC721OfferHanderAddress(erc721OfferHandlerAddress)
        +Call setERC721NftifyAddress(nftify721Address)
        +Call setRecepientAddress(nftifyExchangeProxyAddress)
        +Call addAcceptedToken(tokenAddress) // N1, USDC, USDT

## Upgrade exchangeV1

Configure `.env` file before deploying as below

```
# Production
PROD_KEY=PRIVATE_KEY_HERE

# Environments [local /test / dev / prod]
ENV=prod
```

To deploy NFTExchangeV1 contract

```
npx hardhat run scripts/deploy/deploy-exchangev1.js --network eth/polygon/bscMainnet
```

To deploy SubscribeHandler contract

```
npx hardhat run scripts/deploy/deploy-subscribe.js --network eth/polygon/bscMainnet 
```

All contracts address can be found in [`contracts.json`](./scripts/deploy/data/contracts.json)

To verify NFTExchangeV1 contract

```
npx hardhat run scripts/verify/verify-exchangev1.js --network eth/polygon/bscMainnet
```

To verify SubscribeHandler contract

```
npx hardhat run scripts/verify/verify-subscribe.js --network eth/polygon/bscMainnet 
```


> nvm use 18.18.2