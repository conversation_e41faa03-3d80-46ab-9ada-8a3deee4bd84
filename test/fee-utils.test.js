const { ethers } = require("hardhat");
const { expect } = require("chai");

const data = {
  storeFeeRatio: 0,
  storeAddr: "******************************************",
  artistAddr: "******************************************",
  payouts: ["******************************************"],
  ratios: [10000],
};

const signer = "******************************************";
const signature =
  "0x618918438c052d092f7496512d969dab75a9693b0a57afe57aa32bf7cfbe714721542f3dbb49558a51139127d167304d3f6a2d7a6f15fe8bdf1f4154bc55b5c71b";

describe("TestFeeUtils", function () {
  before(async function () {
    const factory = await ethers.getContractFactory("TestFeeUtils");
    this.contract = await factory.deploy(signer);
    await this.contract.deployed();

    console.log(
      `Contract TestFeeUtils was deployed at ${this.contract.address}`
    );
  });

  it("Result of hashing data must be 32-byte long", async function () {
    const digest = await this.contract.hash(
      data.storeFeeRatio,
      data.storeAddr,
      data.artistAddr,
      data.payouts,
      data.ratios
    );

    console.log("Hashing result: ", digest);
    expect(digest).to.have.lengthOf(66);
  });

  it("Data must be signed correctly", async function () {
    const { recoverAddr, result } = await this.contract.verifyPayoutGroup(
      data.storeFeeRatio,
      data.storeAddr,
      data.artistAddr,
      data.payouts,
      data.ratios,
      signature
    );

    console.log("Verification result:");
    console.log("- Recovered: ", recoverAddr);
    console.log("- Result: ", result);

    expect(result).to.be.true;
  });
});
