const hre = require("hardhat");
const { expect } = require("chai");
const {
  createAirdropEvent,
  createAirdropClaim,
} = require("../scripts/signatures/airdrop-handler");
const { loadProxyContract } = require("../scripts/utils/helpers");
const { EXCHANGE, TEN_DAYS } = require("../scripts/utils/constants");
const { REVERT, OK } = require("./constants");
require("dotenv").config();

describe("AirdropHandler", () => {
  let airdropEvent, airdropClaim;
  let proxy;
  let CLAIM_AMOUNT;
  before(async () => {
    proxy = await loadProxyContract();
    CLAIM_AMOUNT = 3;
  });

  it("Can only claim NFT after the event started", async () => {
    airdropEvent = await createAirdropEvent(
      3,
      true,
      true
      // Math.floor(Date.now() / 1000) + TEN_DAYS
    );
    // console.log(airdropEvent);

    airdropClaim = await createAirdropClaim(airdropEvent, CLAIM_AMOUNT);
    // console.log(airdropClaim);

    console.log(proxy.address);

    // await verify(proxy, airdropClaim.claimData, REVERT);
    try {
      let check = await proxy.claimAirdrop(
        airdropClaim.claimData.data,
        airdropClaim.claimData.addr,
        airdropClaim.claimData.strs,
        airdropClaim.claimData.signatures
      );
      // console.log(check);
    } catch (error) {
      console.error(error);
    }
  }).timeout(100000);
});

const verify = async (proxy, data, check) => {
  if (check == OK) {
    try {
      let executing = await proxy.claimAirdrop(
        data.data,
        data.addr,
        data.strs,
        data.signatures
      );

      console.log(await hre.ethers.provider.getTransaction(executing.hash));

      expect(executing).to.be.ok;

      await executing.wait();
    } catch (error) {
      console.log(error);
    }
  } else if (check == REVERT) {
    try {
      let executing = await proxy.claimAirdrop(
        data.data,
        data.addr,
        data.strs,
        data.signatures
      );

      // console.log(executing);

      console.log(
        await hre.ethers.provider.getTransactionReceipt(executing.hash)
      );
    } catch (error) {
      console.error(error);
    }
    // await expect(
    //   proxy.claimAirdrop(data.data, data.addr, data.strs, data.signatures)
    // ).to.be.reverted;
  }
};
