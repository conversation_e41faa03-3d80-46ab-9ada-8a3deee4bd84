const { expect } = require("chai");
const { ethers } = require("hardhat");
require("dotenv").config();

const { loadContract, loadContractFactory } = require("./helpers");
const { _data_1, _data_2 } = require("./data/buy_handler");
const { EXCHANGE, REVERT, OK } = require("./constants");

describe("Buy Handler", () => {
  let exchange;
  before(async () => {
    const currentProvider = (await ethers.provider.getNetwork()).chainId;
    const CONTRACT_FACTORY = await loadContractFactory(
      EXCHANGE,
      currentProvider
    );
    exchange = loadContract(CONTRACT_FACTORY, currentProvider);
    console.log(exchange.address);
  });

  it("Sale order must not be cancelled before", async () => {
    await verify(exchange, _data_1, REVERT);
  });

  it("Buying amount must be greater than 0", async () => {
    const tmpData = [..._data_2.data];
    _data_2.data[5] = 0;

    await verify(exchange, _data_2, REVERT);
    _data_2.data = tmpData;
  });

  it("Signer must be approved", async () => {
    const tmpAddr = [..._data_2.addr];
    _data_2.addr[3] = _data_2.addr[0];

    await verify(exchange, _data_2, REVERT);
    _data_2.addr = tmpAddr;
  });

  it("The NFT sell request signature must be verified", async () => {
    // Any change in data will cause the signature unverifiable
    const tmpData = [..._data_2.data];
    _data_2.data[1] = 100000;

    await verify(exchange, _data_2, REVERT);
    _data_2.data = tmpData;
  });

  it("If ERC721, the soldQuantity must be 0", async () => {
    if (_data_2.data[6] == 1) {
      expect(await exchange.soldQuantity(_data_2.data[0])).to.be.equal(0);
    }
  });

  it("If ERC1155, the remain amount must be available", async () => {
    if (_data_2.data[6] == 0) {
      const remainAmount =
        _data_2.data[2] -
        (await exchange.soldQuantityBySaleOrder(_data_2.signatures[1]));

      expect(remainAmount >= _data_2.data[5]).to.be.true;
    }
  });

  it("After above check, the sample data must execute successfully", async () => {
    const currentSold = parseInt(await exchange.soldQuantity(_data_2.data[0]));
    const currentSoldBySaleOrder = parseInt(
      await exchange.soldQuantityBySaleOrder(_data_2.signatures[1])
    );

    try {
      let executing = await exchange.buyNowNative(
        _data_2.data,
        _data_2.addr,
        _data_2.strs,
        _data_2.signatures
      );

      await executing.wait();
    } catch (error) {
      console.log(error);
    }

    expect(await exchange.soldQuantity(_data_2.data[0])).to.be.equal(
      _data_2.data[5] + currentSold
    );

    expect(
      await exchange.soldQuantityBySaleOrder(_data_2.signatures[1])
    ).to.be.equal(_data_2.data[5] + currentSoldBySaleOrder);
  }).timeout(100000);

  // it("If using ERC20 token, the payment token must be approved", async () => {
  //   expect(await exchange.acceptedTokens(_data_2.addr[1])).to.be.true;
  // });
});

const verify = async (exchange, data, check) => {
  if (check == OK) {
    expect(
      await exchange.buyNowNative(
        data.data,
        data.addr,
        data.strs,
        data.signatures
      )
    ).to.be.ok;
  } else if (check == REVERT) {
    await expect(
      exchange.buyNowNative(data.data, data.addr, data.strs, data.signatures)
    ).to.be.reverted;
  }
};
