const { expect } = require("chai");
const { ethers, helpers } = require("hardhat");
const {time} = require("@nomicfoundation/hardhat-network-helpers");
require("dotenv").config();

describe("NFTify721SBT Contract", () => {
  let sbtContract;
  let owner;
  let controller;
  let user1;
  let user2;
  let addrs;

  const TOKEN_NAME = "NFTify Soulbound Token";
  const TOKEN_SYMBOL = "NFTSBT";
  const BASE_URI = "https://api.nftify.com/metadata/";
  const LOCK_DURATION = 86400; // 1 day in seconds

  beforeEach(async () => {
    // Get signers
    [owner, controller, user1, user2, ...addrs] = await ethers.getSigners();

    // Deploy NFTify721SBT contract
    const NFTify721SBT = await ethers.getContractFactory("NFTify721SBT");
    sbtContract = await NFTify721SBT.deploy(
      TOKEN_NAME, 
      TOKEN_SYMBOL, 
      BASE_URI, 
      controller.address,
      LOCK_DURATION
    );
    await sbtContract.waitForDeployment();
  });

  describe("Deployment", () => {
    it("Should set the correct name and symbol", async () => {
      expect(await sbtContract.name()).to.equal(TOKEN_NAME);
      expect(await sbtContract.symbol()).to.equal(TOKEN_SYMBOL);
    });

    it("Should set the correct baseURI", async () => {
      expect(await sbtContract.baseURI()).to.equal(BASE_URI);
    });

    it("Should set the deployer as owner", async () => {
      expect(await sbtContract.owner()).to.equal(owner.address);
    });

    it("Should set the controller correctly", async () => {
      expect(await sbtContract.controller()).to.equal(controller.address);
    });
  });

  describe("Minting", () => {
    it("Should allow owner to mint tokens", async () => {
      await sbtContract.mint(user1.address, 1, "0x");
      
      expect(await sbtContract.ownerOf(1)).to.equal(user1.address);
      expect(await sbtContract.balanceOf(user1.address)).to.equal(1n);
    });

    it("Should allow controller to mint tokens", async () => {
      await sbtContract.connect(controller).mint(user1.address, 2, "0x");
      
      expect(await sbtContract.ownerOf(2)).to.equal(user1.address);
    });

    it("Should reject minting from unauthorized accounts", async () => {
      let failed = false;
      try {
        await sbtContract.connect(user1).mint(user2.address, 3, "0x");
      } catch (error) {
        failed = true;
        expect(error.message).to.include("only owner or controller");
      }
      expect(failed).to.be.true;
    });

    it("Should set unlock time when minting", async () => {
      await sbtContract.mint(user1.address, 4, "0x");
      
      const unlockTime = await sbtContract.nftUnlockTime(4);
      const blockTimestamp = (await ethers.provider.getBlock('latest')).timestamp;
      
      // Convert both to strings or numbers for comparison
      expect(Number(unlockTime)).to.equal(blockTimestamp + LOCK_DURATION);
    });
  });

  describe("Soulbound Functionality", () => {
    beforeEach(async () => {
      // Mint a token to user1 for transfer tests
      await sbtContract.mint(user1.address, 1, "0x");
    });

    it("Should prevent transfer via transferFrom", async () => {
      let failed = false;
      try {
        await sbtContract.connect(user1).transferFrom(user1.address, user2.address, 1);
      } catch (error) {
        failed = true;
        expect(error.message).to.include("cannot be transferred");
      }
      expect(failed).to.be.true;
    });

    it("Should prevent transfer via safeTransferFrom", async () => {
      let failed = false;
      try {
        await sbtContract.connect(user1)["safeTransferFrom(address,address,uint256)"](
          user1.address, user2.address, 1
        );
      } catch (error) {
        failed = true;
        expect(error.message).to.include("cannot be transferred");
      }
      expect(failed).to.be.true;
    });

    it("Should prevent transfer via safeTransferFrom with data", async () => {
      let failed = false;
      try {
        await sbtContract.connect(user1)["safeTransferFrom(address,address,uint256,bytes)"](
          user1.address, user2.address, 1, "0x"
        );
      } catch (error) {
        failed = true;
        expect(error.message).to.include("cannot be transferred");
      }
      expect(failed).to.be.true;
    });

    it("Should prevent transfer even if approved", async () => {
      // Approve user2 to manage user1's token
      await sbtContract.connect(user1).approve(user2.address, 1);
      
      // Check approval was successful
      expect(await sbtContract.getApproved(1)).to.equal(user2.address);
      
      // Try to transfer with approval - should still fail
      let failed = false;
      try {
        await sbtContract.connect(user2).transferFrom(user1.address, user2.address, 1);
      } catch (error) {
        failed = true;
        expect(error.message).to.include("cannot be transferred");
      }
      expect(failed).to.be.true;
    });

    it("Should prevent transfer even with approvalForAll", async () => {
      // Set approval for all user1's tokens
      await sbtContract.connect(user1).setApprovalForAll(user2.address, true);
      
      // Check approval was successful
      expect(await sbtContract.isApprovedForAll(user1.address, user2.address)).to.equal(true);
      
      // Try to transfer with approvalForAll - should still fail
      let failed = false;
      try {
        await sbtContract.connect(user2).transferFrom(user1.address, user2.address, 1);
      } catch (error) {
        failed = true;
        expect(error.message).to.include("cannot be transferred");
      }
      expect(failed).to.be.true;
    });

    it("Should prevent transfer to zero address (burning via transfer)", async () => {
      let failed = false;
      try {
        await sbtContract.connect(user1).transferFrom(user1.address, ethers.ZeroAddress, 1);
      } catch (error) {
        failed = true;
        expect(error.message).to.include("NFTify721SBT: SBT tokens cannot be transferred");
      }
      expect(failed).to.be.true;
    });
  });

  describe("Burning", () => {
    beforeEach(async () => {
      // Mint a token to user1 for burning tests
      await sbtContract.mint(user1.address, 1, "0x");
    });

    it("Should allow token owner to burn their token", async () => {
      await time.increase(LOCK_DURATION);
      await sbtContract.connect(user1).burn(1);
      
      // Check that token no longer exists
      let failed = false;
      try {
        await sbtContract.ownerOf(1);
      } catch (error) {
        failed = true;
        expect(error.message).to.include("ERC721NonexistentToken");
      }
      expect(failed).to.be.true;

      await sbtContract.mint(user1.address, 1, "0x");
      console.log("fdsafdsa", await sbtContract.ownerOf(1));
    });

    it("Should allow approved address to burn token", async () => {
      // Approve user2 to manage user1's token
      await time.increase(LOCK_DURATION);
      await sbtContract.connect(user1).approve(user2.address, 1);
      
      // Burn the token
      await sbtContract.connect(user2).burn(1);
      
      // Check that token no longer exists
      let failed = false;
      try {
        await sbtContract.ownerOf(1);
      } catch (error) {
        failed = true;
        expect(error.message).to.include("ERC721NonexistentToken");
      }
      expect(failed).to.be.true;
    });

    it("Should prevent unauthorized burning", async () => {
      let failed = false;
      try {
        await sbtContract.connect(user2).burn(1);
      } catch (error) {
        failed = true;
        expect(error.message).to.include("not owner nor approved");
      }
      expect(failed).to.be.true;
    });
  });

  describe("Admin Functions", () => {
    it("Should allow owner to set new baseURI", async () => {
      const newURI = "https://new-api.nftify.com/metadata/";
      
      await sbtContract.setBaseURI(newURI);
      
      expect(await sbtContract.baseURI()).to.equal(newURI);
    });

    it("Should prevent non-owner from setting baseURI", async () => {
      let failed = false;
      try {
        await sbtContract.connect(user1).setBaseURI("https://fake.com/");
      } catch (error) {
        failed = true;
        expect(error.message).to.include("OwnableUnauthorizedAccount");
      }
      expect(failed).to.be.true;
    });

    it("Should allow owner to set new controller", async () => {
      await sbtContract.setController(user1.address);
      expect(await sbtContract.controller()).to.equal(user1.address);
    });

    it("Should prevent non-owner from setting controller", async () => {
      let failed = false;
      try {
        await sbtContract.connect(user1).setController(user2.address);
      } catch (error) {
        failed = true;
        expect(error.message).to.include("OwnableUnauthorizedAccount");
      }
      expect(failed).to.be.true;
    });

    it("Should allow owner to pause and unpause the contract", async () => {
      // Pause the contract
      await sbtContract.pause();
      expect(await sbtContract.paused()).to.equal(true);
      
      // Try to mint while paused
      let failed = false;
      try {
        await sbtContract.mint(user1.address, 5, "0x");
      } catch (error) {
        failed = true;
        expect(error.message).to.include("paused");
      }
      expect(failed).to.be.true;
      
      // Unpause the contract
      await sbtContract.unpause();
      expect(await sbtContract.paused()).to.equal(false);
      
      // Should be able to mint now
      await sbtContract.mint(user1.address, 5, "0x");
      expect(await sbtContract.ownerOf(5)).to.equal(user1.address);
    });

    it("Should prevent non-owner from pausing/unpausing", async () => {
      let failed = false;
      try {
        await sbtContract.connect(user1).pause();
      } catch (error) {
        failed = true;
        expect(error.message).to.include("OwnableUnauthorizedAccount");
      }
      expect(failed).to.be.true;
      
      failed = false;
      try {
        await sbtContract.connect(user1).unpause();
      } catch (error) {
        failed = true;
        expect(error.message).to.include("OwnableUnauthorizedAccount");
      }
      expect(failed).to.be.true;
    });
  });

  describe("Token URI and Metadata", () => {
    beforeEach(async () => {
      // Mint a token to user1
      await sbtContract.mint(user1.address, 10, "0x");
    });

    it("Should return the correct tokenURI", async () => {
      expect(await sbtContract.tokenURI(10)).to.equal(`${BASE_URI}10`);
    });

    it("Should update tokenURI when baseURI changes", async () => {
      const newURI = "https://new-api.nftify.com/metadata/";
      await sbtContract.setBaseURI(newURI);
      
      expect(await sbtContract.tokenURI(10)).to.equal(`${newURI}10`);
    });
  });
});

