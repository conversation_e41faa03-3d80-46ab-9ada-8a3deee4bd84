const { ethers } = require("hardhat");
require("dotenv").config();

const {
  BUY_HANDLER,
  SELL_HANDLER,
  OFFER_HANDLER,
  EXCHANGE,
  BSCTESTNET,
  MUMBAI,
  RINKEBY,
} = require("./constants");

const detectContract = (contract) => {
  switch (contract) {
    case BUY_HANDLER:
      return "handlers/BuyHandler.sol:BuyHandler";
    case SELL_HANDLER:
      return "handlers/SellHandler.sol:SellHandler";
    case OFFER_HANDLER:
      return "handlers/OfferHandler.sol:OfferHandler";
    case EXCHANGE:
      return "exchanges/NFTExchangeV1.sol:NFTExchangeV1";
  }
};

const detectNetwork = (chainid) => {
  switch (chainid) {
    case RINKEBY:
      return "ethereum";
    case BSCTESTNET:
      return "bsc";
    case MUMBAI:
      return "polygon";
    case LOCAL:
      return "local";
  }
};

const loadContractFactory = async (contract, chainid) => {
  const _network = detectNetwork(chainid);
  const _contract = detectContract(contract);

  return await ethers.getContractFactory(`contracts/src/${_contract}`);
};

const loadContract = (factory, chainid) => {
  let PROXY_ADDRESS;
  switch (chainid) {
    case RINKEBY:
      PROXY_ADDRESS = process.env.ETH_PROXY;
      break;
    case BSCTESTNET:
      PROXY_ADDRESS = process.env.BSC_PROXY;
      break;
    case MUMBAI:
      PROXY_ADDRESS = process.env.POLYGON_PROXY;
      break;
    case LOCAL:
      PROXY_ADDRESS = process.env.LOCAL_PROXY;
      break;
  }

  return factory.attach(PROXY_ADDRESS);
};

const generateTestData = (data, addr, strs, signatures) => ({
  data: data,
  addr: addr,
  strs: strs,
  signatures: signatures,
});

module.exports = {
  loadContract,
  loadContractFactory,
  generateTestData,
};
