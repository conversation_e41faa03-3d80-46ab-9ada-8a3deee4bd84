const { ethers } = require("hardhat");
const { expect } = require("chai");

const data = {
  recipient: "******************************************",
  token: "******************************************",
  amount: "19000000000000000000",
  _id: "0x6440fc63c93340a3bf333018",
};

const signer = "******************************************";
const signature =
  "0xa803e63a13c0bf890f25abda186179035f14174e340fe1950fd6dba9feda67e36816b61469e7ecf155d190b6ddba14c3d67358963f7ceb0e96dfe491e5e726a81b";

describe("TestSubscribeHandler", function () {
  before(async function () {
    const factory = await ethers.getContractFactory("TestSubscribeHandler");
    this.contract = await factory.deploy(signer, signer);
    await this.contract.deployed();

    console.log(
      `Contract SubscribeHandler was deployed at ${this.contract.address}`
    );
  });

  it("Result of hashing data must be 32-byte long", async function () {
    const digest = await this.contract.hash(
      data.recipient,
      data.token,
      data.amount,
      data._id
    );

    console.log("Hashing result: ", digest);
    expect(digest).to.have.lengthOf(66);
  });

  it("Data must be signed correctly", async function () {
    const { recoverAddr, result } = await this.contract.verifySignature(
      data.recipient,
      data.token,
      data.amount,
      data._id,
      signature
    );

    console.log("Verification result:");
    console.log("- Recovered: ", recoverAddr);
    console.log("- Result: ", result);

    expect(result).to.be.true;
  });
});
