const { generateTestData } = require("../helpers");

const _data_1 = generateTestData(
  [
    "0x7242f542186bddfe3180f102290d38b3552c47bf9f00204dc38814a670366735",
    1000,
    "50",
    "2000000000000000000",
    "0",
    1,
    0,
    0,
    "100",
    0,
    0,
  ],
  [
    "0xdA8AB4137fE28f969b27C780D313d1bb62C8341E",
    "0x86Df7983E9eebe819A3d60Bc5BA2302858A6E586",
    "0x8B6E7341CD898B04bA0354eBED3a0E3E557d368E",
    "0xdA8AB4137fE28f969b27C780D313d1bb62C8341E",
    "0xc38470D323a77A70AA0dCcaB97D29965cc67D88E",
    "0xdA8AB4137fE28f969b27C780D313d1bb62C8341E",
    "0xCB4D8adf4ceb37e86300Edd196200f99F7a9B85d",
  ],
  ["62c50d8419716d3a8b0122ac"],
  [
    "0x6cbb593d5a07a4b344a353c09713d2dc47d54fa5539542f39edb953ba45e8e3120509bd85f0ae374419605146776cd218b127f216bd8e1d526a72227a760997a1b",
    "0xbe1610b4d515c9206157e1c0a1114e6c696a1ea37bac7193978471974c2a5d460028ca3aab867e9867d555e0a4250ee363e78cc5dc199cec6eac0ac533076bca1b",
    "0xc4a1ad613730dc7ba950908db0f23d5699c715c75c7f08ae54f05490feecb6cf0523bab2be137ad18b8c328f8172fd0072c4c060032cd19a0ec8335f58974f4a1b",
  ]
);

const _data_2 = generateTestData(
  [
    "0x7242f542186bddfe3180f102290d38b3552c47bf9f00204dc38814a670366735",
    1000,
    "50",
    "3000000000000000000",
    "0",
    1,
    0,
    0,
    "100",
    0,
    0,
  ],
  [
    "0xdA8AB4137fE28f969b27C780D313d1bb62C8341E",
    "0x86Df7983E9eebe819A3d60Bc5BA2302858A6E586",
    "0x8B6E7341CD898B04bA0354eBED3a0E3E557d368E",
    "0xdA8AB4137fE28f969b27C780D313d1bb62C8341E",
    "0xc38470D323a77A70AA0dCcaB97D29965cc67D88E",
    "0xdA8AB4137fE28f969b27C780D313d1bb62C8341E",
    "0xCB4D8adf4ceb37e86300Edd196200f99F7a9B85d",
  ],
  ["62c50e5819716d3a8b0122ae"],
  [
    "0xd7e454a7538d9e7f413c38e638affd4c5d8388054d018e4f65522efe20a510d3578afb1b70565ee3069ad96007e14489d7a99fc18a28e939738be1a7488d7aca1b",
    "0x386c07e5ce13b96e9f3a8ba519820bffde0864a7d517e3301ccf1e004031241f1ecc7f660c51e11894f5aafd70b449d1a0327817fbb57db6bb88d42e279623d21c",
    "0xc4a1ad613730dc7ba950908db0f23d5699c715c75c7f08ae54f05490feecb6cf0523bab2be137ad18b8c328f8172fd0072c4c060032cd19a0ec8335f58974f4a1b",
  ]
);

module.exports = {
  _data_1,
  _data_2,
};
