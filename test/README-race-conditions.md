# NFT Buy Handler - Race Condition Tests

This document describes the comprehensive TypeScript unit tests for race condition scenarios with limited edition NFTs in the BuyHandler.sol and NFTExchangeV1.sol contracts.

## Overview

The race condition tests simulate real-world scenarios where multiple buyers attempt to purchase limited edition NFTs simultaneously, ensuring the smart contracts handle these edge cases correctly and maintain data integrity.

## Test Structure

### Test File: `test/race-condition-buy.test.ts`

The test file includes comprehensive TypeScript tests that cover:

1. **Single Edition Race Condition (ERC721)**
2. **Limited Edition Partial Fulfillment (ERC1155)**
3. **Edge Cases and Additional Validations**

## Test Cases

### Test Case 1: Single Edition Race Condition (ERC721)

**Scenario**: Two buyers attempt to purchase the same single edition NFT simultaneously.

**Expected Behavior**:
- Exactly 1 transaction succeeds
- 1 transaction fails with "ERC721 token has been sold" error
- The successful buyer receives the NFT
- The failed transaction is properly reverted
- `soldQuantity` mapping is correctly updated

**Implementation**:
```typescript
it("Should allow only one buyer to succeed when purchasing the same single edition NFT", async function () {
  // Creates sale order for exactly 1 edition
  // Simulates 2 simultaneous buy transactions
  // Verifies only one succeeds and one fails with correct error
});
```

### Test Case 2: Limited Edition Partial Fulfillment (ERC1155)

**Scenario**: Multiple buyers attempt to purchase from a limited supply of 3 editions.

**Test 2a**: Partial Fulfillment Failure
- First buyer purchases 2 editions (succeeds)
- Second buyer attempts to purchase 2 editions (fails - only 1 remaining)
- Verifies "Not have enough ERC155 available" error
- Ensures total sold quantity doesn't exceed limit

**Test 2b**: Successful Remaining Purchase
- First buyer purchases 2 editions
- Second buyer purchases remaining 1 edition (succeeds)
- Verifies both buyers receive correct amounts
- Confirms total sold equals total supply

**Implementation**:
```typescript
it("Should handle partial fulfillment correctly with limited edition NFTs", async function () {
  // First buyer buys 2 out of 3 editions
  // Second buyer tries to buy 2 (should fail)
  // Verifies correct error and state
});

it("Should allow successful purchase of remaining single edition", async function () {
  // First buyer buys 2 out of 3 editions  
  // Second buyer buys remaining 1 (should succeed)
  // Verifies final state
});
```

## Key Features

### Proper TypeScript Types
- Uses generated typechain types for all contracts
- Strongly typed function parameters and return values
- Type-safe contract interactions

### Mock NFT Contracts
- Deploys fresh NFTify721 and NFTify1155 contracts for each test
- Sets up proper controller permissions
- Configures realistic test scenarios

### Comprehensive Validation
- Tests `soldQuantity` and `soldQuantityBySaleOrder` mappings
- Verifies NFT ownership transfers
- Checks error messages match expected values
- Validates remaining supply calculations

### Edge Case Testing
- Expired transaction attempts
- Zero buying amount validation
- Invalid signer verification
- Proper revert message assertions

## Test Setup

### Contract Deployment
Each test deploys fresh instances of:
- `NFTify721` (ERC721 implementation)
- `NFTify1155` (ERC1155 implementation)  
- `NFTBuyHandler` (Buy logic handler)
- `NFTFeeUtils` (Fee calculation utilities)
- `NFTExchangeV1` (Main exchange contract)

### Signer Configuration
- `owner`: Contract deployer and admin
- `signer`: Authorized signature validator
- `buyer1`, `buyer2`: Test buyers for race conditions
- `creator`: NFT creator/artist
- `storeAddress`: Store fee recipient
- `payoutAddress`: Payout recipient

### Helper Functions

#### `createBuyRequestData()`
Creates properly formatted data arrays for `buyNowNative()` calls:
- Handles both ERC721 and ERC1155 token types
- Configures sell order supply and buying amounts
- Sets up payout ratios and addresses

#### `createBuySignature()`
Generates valid ECDSA signatures for buy requests:
- Uses the authorized signer's private key
- Follows the exact hash format expected by contracts
- Supports different receiver addresses per buyer

## Running the Tests

### Prerequisites
```bash
npm install
npx hardhat compile
```

### Run Race Condition Tests
```bash
# Run only the race condition tests
npm run test:race-conditions

# Or run all tests
npm test

# Run with specific network (if needed)
npx hardhat test test/race-condition-buy.test.ts --network hardhat
```

### Expected Output
```
NFT Buy Handler - Race Condition Tests
  Test Case 1: Single Edition Race Condition (ERC721)
    ✓ Should allow only one buyer to succeed when purchasing the same single edition NFT

  Test Case 2: Limited Edition Partial Fulfillment (ERC1155)  
    ✓ Should handle partial fulfillment correctly with limited edition NFTs
    ✓ Should allow successful purchase of remaining single edition after partial fulfillment

  Edge Cases and Additional Validations
    ✓ Should revert when trying to buy expired NFT
    ✓ Should revert when buying amount is zero
    ✓ Should revert with invalid signer

  6 passing
```

## Technical Implementation Details

### Race Condition Simulation
The tests simulate race conditions by:
1. Creating identical buy requests for multiple buyers
2. Executing transactions in rapid succession
3. Verifying that contract state remains consistent
4. Ensuring proper error handling for failed transactions

### State Verification
Each test verifies:
- NFT balance changes for all parties
- Contract mapping updates (`soldQuantity`, `soldQuantityBySaleOrder`)
- Event emissions with correct parameters
- Proper error messages for failed transactions

### Gas and Value Handling
Tests include proper ETH value calculations:
- Sell order price + shipping fees
- Payout distribution verification
- Fee calculation accuracy

## Troubleshooting

### Common Issues
1. **Signature Verification Failures**: Ensure the signer is properly authorized in the contract
2. **Insufficient ETH Value**: Check that test transactions include enough ETH for price + fees
3. **Contract Address Mismatches**: Verify mock contracts are properly deployed and configured
4. **Type Errors**: Ensure all contract types are properly imported from typechain

### Debug Tips
- Use `console.log()` in tests to inspect transaction data
- Check contract events for detailed execution information
- Verify contract state before and after transactions
- Use Hardhat's built-in debugging tools for transaction traces

## Integration with CI/CD

These tests can be integrated into continuous integration pipelines:

```yaml
# Example GitHub Actions workflow
- name: Run Race Condition Tests
  run: |
    npm ci
    npx hardhat compile
    npm run test:race-conditions
```

The tests are designed to be deterministic and should pass consistently in automated environments.
