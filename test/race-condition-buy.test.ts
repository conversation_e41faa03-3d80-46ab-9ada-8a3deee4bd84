import { expect } from "chai";
import { ethers, network } from "hardhat";
import { SignerWithAddress } from "@nomicfoundation/hardhat-ethers/signers";
import { ContractTransactionResponse, ContractTransactionReceipt} from "ethers";
import {
  NFTExchangeV1,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  NFTify721,
  NFTify1155,
  NFT<PERSON>eeUtils
} from "../typechain-types";

// Helper function to create signature (simplified version)
async function createSignature(values: string[], types: string[], signer: SignerWithAddress): Promise<string> {
  const hash = ethers.solidityPackedKeccak256(types, values);
  return await signer.signMessage(ethers.getBytes(hash));
}

type Transaction = Promise<ContractTransactionResponse>;
type TransactionReceipt = ContractTransactionReceipt;

async function sendTransactionsInSameBlock(
  transactions: Transaction[]
): Promise<TransactionReceipt[]> {
  await network.provider.send("evm_setAutomine", [false]);

  // Send all transactions to mempool (don't await yet)
  const txPromises = transactions;

  // Mine a block to include all pending transactions
  await network.provider.send("evm_mine");

  // Now await the transaction responses and get receipts
  const txResponses = await Promise.all(txPromises);
  const receipts = await Promise.all(
    txResponses.map((tx: ContractTransactionResponse) => tx.wait())
  );

  return receipts.filter((receipt): receipt is ContractTransactionReceipt => receipt !== null);
}

describe("NFT Buy Handler - Race Condition Tests", function () {
  let nftExchange: NFTExchangeV1;
  let buyHandler: NFTBuyHandler;
  let feeUtils: NFTFeeUtils;
  let nft721: NFTify721;
  let nft1155: NFTify1155;
  
  let owner: SignerWithAddress;
  let signer: SignerWithAddress;
  let buyer1: SignerWithAddress;
  let buyer2: SignerWithAddress;
  let creator: SignerWithAddress;
  let storeAddress: SignerWithAddress;
  let payoutAddress: SignerWithAddress;

  const ERC_721 = 1;
  const ERC_1155 = 0;
  const TOKEN_ID = 1;
  const SELL_ORDER_PRICE = BigInt(0); // Set to 0 to bypass hardcoded fee transfer failure in BuyHandler
  const SHIPPING_FEE = ethers.parseEther("0.01");
  const EXPIRED_AT = Math.floor(Date.now() / 1000) + 3600; // 1 hour from now

  // For fee calculation: transferAmount = sellOrderPrice * buyingAmount
  const calculateTotalValue = (sellOrderPrice: bigint, buyingAmount: number, shippingFee: bigint) => {
    const transferAmount = sellOrderPrice * BigInt(buyingAmount);
    return transferAmount + shippingFee;
  };

  beforeEach(async function () {
    [owner, signer, buyer1, buyer2, creator, storeAddress, payoutAddress] = await ethers.getSigners();

    // Deploy NFTExchangeV1 implementation
    const NFTExchangeV1Factory = await ethers.getContractFactory("contracts/src/exchanges/NFTExchangeV1.sol:NFTExchangeV1");
    const nftExchangeImpl = await NFTExchangeV1Factory.deploy();
    await nftExchangeImpl.waitForDeployment();

    // Deploy NFTExchangeProxy
    const NFTExchangeProxyFactory = await ethers.getContractFactory("NFTExchangeProxy");
    const nftExchangeProxy = await NFTExchangeProxyFactory.deploy();
    await nftExchangeProxy.waitForDeployment();

    // Upgrade proxy to use NFTExchangeV1 implementation
    await nftExchangeProxy.upgradeTo(await nftExchangeImpl.getAddress());

    // Set admin permissions on proxy
    await nftExchangeProxy.setAdminList(owner.address, true);

    // Attach NFTExchangeV1 interface to proxy address
    nftExchange = NFTExchangeV1Factory.attach(await nftExchangeProxy.getAddress()) as any as NFTExchangeV1;

    // Deploy handler contracts
    const BuyHandlerFactory = await ethers.getContractFactory("contracts/src/handlers/BuyHandler.sol:NFTBuyHandler");
    buyHandler = await BuyHandlerFactory.deploy() as any as NFTBuyHandler;
    await buyHandler.waitForDeployment();

    const FeeUtilsFactory = await ethers.getContractFactory("contracts/src/utils/FeeUtils.sol:NFTFeeUtils");
    feeUtils = await FeeUtilsFactory.deploy() as any as NFTFeeUtils;
    await feeUtils.waitForDeployment();

    // Setup exchange contract with handlers
    await nftExchange.setBuyHandler(await buyHandler.getAddress());
    await nftExchange.setFeeUtils(await feeUtils.getAddress());
    await nftExchange.setSigner(signer.address, true);

    // Also set the signer in the proxy's signatureUtils
    await nftExchange.setSignatureUtils(signer.address);

    // Deploy NFT collections with proxy as controller
    const NFTify721Factory = await ethers.getContractFactory("contracts/collections/NFTify721.sol:NFTify721");
    nft721 = await NFTify721Factory.deploy(
      "Test NFT 721",
      "T721",
      "https://test.com/",
      await nftExchange.getAddress(), // Use proxy address as controller
      0 // no lock duration for tests
    ) as any as NFTify721;
    await nft721.waitForDeployment();

    const NFTify1155Factory = await ethers.getContractFactory("contracts/collections/NFTify1155.sol:NFTify1155");
    nft1155 = await NFTify1155Factory.deploy(
      "Test NFT 1155",
      "T1155",
      "https://test.com/",
      await nftExchange.getAddress() // Use proxy address as controller
    ) as any as NFTify1155;
    await nft1155.waitForDeployment();
  });

  /**
   * Helper function to create a sell order signature
   * This represents the seller listing the NFT for sale
   */
  async function createSellOrderSignature(tokenId: number, sellOrderSupply: number, sellOrderPrice: string, contractAddress: string): Promise<string> {
    // Create a simple sell order signature using the seller's private key
    // In a real system, this would be created when the seller lists the NFT
    const sellOrderData = [
      tokenId.toString(),
      sellOrderSupply.toString(),
      sellOrderPrice,
      contractAddress
    ];

    const sellOrderTypes = [
      "uint256", // tokenID
      "uint256", // sellOrderSupply
      "uint256", // sellOrderPrice
      "address"  // contractAddress
    ];

    const sellOrderHash = ethers.solidityPackedKeccak256(sellOrderTypes, sellOrderData);
    return await creator.signMessage(ethers.getBytes(sellOrderHash));
  }

  /**
   * Helper function to create buy request data arrays
   */
  function createBuyRequestData(
    tokenType: number,
    sellOrderSupply: number,
    buyingAmount: number,
    contractAddress: string,
    buyer: SignerWithAddress,
  ) {
    const data = [
      TOKEN_ID,           // [0] tokenID
      buyingAmount,       // [1] quantity  
      sellOrderSupply,    // [2] sellOrderSupply
      SELL_ORDER_PRICE,   // [3] sellOrderPrice
      1,                  // [4] enableMakeOffer
      buyingAmount,       // [5] buyingAmount
      tokenType,          // [6] tokenType
      0,                  // [7] partnerType
      0,                  // [8] partnerFee
      0,                  // [9] transactionType
      0,                  // [10] storeFeeRatio
      SHIPPING_FEE,       // [11] shippingFee
      EXPIRED_AT,         // [12] expiredAt
      10000               // [13] payoutRatio (100%)
    ];

    const addresses = [
      creator.address,      // [0] creator
      ethers.ZeroAddress,   // [1] tokenAddress (native ETH)
      contractAddress,      // [2] contractAddress (NFT collection address)
      signer.address,       // [3] signer
      storeAddress.address, // [4] storeAddress
      buyer.address,       // [5] receiver (will be overridden per buyer)
      payoutAddress.address // [6] payoutAddress
    ];

    return { data, addresses };
  }


  describe("Test Case 1: Single Edition Race Condition (ERC721)", function () {
    it("Should allow a single successful purchase first", async function () {
      const sellOrderSignature = await createSellOrderSignature(TOKEN_ID, 1, SELL_ORDER_PRICE.toString(), await nft721.getAddress());
      const { data: data1, addresses: addresses1 } = createBuyRequestData(
        ERC_721,
        1, // sellOrderSupply: exactly 1 edition
        1, // buyingAmount: 1 edition
        await nft721.getAddress(),
        buyer1
      );
    
      // Create signature for buyer1 using the exact format from signBuyRq.ts
      const signature1 = await createSignature(
        [
          data1[0].toString(), // tokenID
          data1[1].toString(), // quantity
          data1[2].toString(), // sellOrderSupply
          data1[3].toString(), // sellOrderPrice
          data1[4].toString(), // enableMakeOffer
          data1[6].toString(), // tokenType
          data1[7].toString(), // partnerType
          data1[8].toString(), // partnerFee
          data1[9].toString(), // transactionType
          data1[11].toString(), // shippingFee
          data1[12].toString(), // expiredAt
          addresses1[0], // creator
          addresses1[5], // receiver
          ethers.ZeroAddress, // tokenAddress (native ETH)
          addresses1[2], // contractAddress (NFT collection)
          sellOrderSignature, // sellOrderSignature
        ],
        [
          "uint256", "uint256", "uint256", "uint256", "uint256", // tokenID, quantity, sellOrderSupply, sellOrderPrice, enableMakeOffer
          "uint256", "uint256", "uint256", "uint256", // tokenType, partnerType, partnerFee, transactionType
          "uint256", "uint256", // shippingFee, expiredAt
          "address", "address", "address", "address", // creator, receiver, tokenAddress, contractAddress
          "bytes", // sellOrderSignature
        ],
        signer
      );

      addresses1[5] = buyer1.address;
      const bytesArray1 = [signature1, sellOrderSignature, "0x5678"];

      const { data: data2, addresses: addresses2 } = createBuyRequestData(
        ERC_721,
        1, // sellOrderSupply: exactly 1 edition
        1, // buyingAmount: 1 edition
        await nft721.getAddress(),
        buyer2
      );
    
      // Create signature for buyer1 using the exact format from signBuyRq.ts
      const signature2 = await createSignature(
        [
          data2[0].toString(), // tokenID
          data2[1].toString(), // quantity
          data2[2].toString(), // sellOrderSupply
          data2[3].toString(), // sellOrderPrice
          data2[4].toString(), // enableMakeOffer
          data2[6].toString(), // tokenType
          data2[7].toString(), // partnerType
          data2[8].toString(), // partnerFee
          data2[9].toString(), // transactionType
          data2[11].toString(), // shippingFee
          data2[12].toString(), // expiredAt
          addresses2[0], // creator
          addresses2[5], // receiver
          ethers.ZeroAddress, // tokenAddress (native ETH)
          addresses2[2], // contractAddress (NFT collection)
          sellOrderSignature, // sellOrderSignature
        ],
        [
          "uint256", "uint256", "uint256", "uint256", "uint256", // tokenID, quantity, sellOrderSupply, sellOrderPrice, enableMakeOffer
          "uint256", "uint256", "uint256", "uint256", // tokenType, partnerType, partnerFee, transactionType
          "uint256", "uint256", // shippingFee, expiredAt
          "address", "address", "address", "address", // creator, receiver, tokenAddress, contractAddress
          "bytes", // sellOrderSignature
        ],
        signer
      );
      addresses2[5] = buyer2.address;
      const bytesArray2 = [signature2, sellOrderSignature, "0x5678"];

      const stringArray = ["internal-tx-1"];
      const totalValue = calculateTotalValue(SELL_ORDER_PRICE, 1, SHIPPING_FEE);

      // Execute transaction - match the exact calling pattern from signBuyRq.ts
      const tx1 = nftExchange.connect(buyer1).buyNowNative(
        data1,
        addresses1,
        stringArray,
        bytesArray1,
        { value: totalValue }
      );

      const tx2 = nftExchange.connect(buyer2).buyNowNative(
        data2,
        addresses2,
        stringArray,
        bytesArray2,
        { value: totalValue }
      );

      const receipts = await sendTransactionsInSameBlock([tx1, tx2]);

      console.log(receipts);

      // Verify NFT ownership
      expect(await nft721.ownerOf(TOKEN_ID)).to.equal(buyer1.address);

      // Verify sold quantity
      expect(await nftExchange.soldQuantity(await nft721.getAddress(), TOKEN_ID)).to.equal(1);
    });
  });

  describe("Test Case 2: Limited Edition Partial Fulfillment (ERC1155)", function () {
    it("Should handle partial fulfillment correctly with limited edition NFTs", async function () {
      const TOTAL_SUPPLY = 3;
      const FIRST_BUY_AMOUNT = 2;
      const SECOND_BUY_AMOUNT = 2; // This should fail since only 1 remains 

      const sellOrderSignature = await createSellOrderSignature(TOKEN_ID, TOTAL_SUPPLY, SELL_ORDER_PRICE.toString(), await nft1155.getAddress());
      const { data: baseData, addresses: baseAddresses } = createBuyRequestData(
        ERC_1155,
        TOTAL_SUPPLY,
        FIRST_BUY_AMOUNT,
        await nft1155.getAddress(),
        buyer1
      );

      // First buyer attempts to purchase 2 editions
      const data1 = [...baseData];
      data1[1] = FIRST_BUY_AMOUNT; // quantity
      data1[5] = FIRST_BUY_AMOUNT; // buyingAmount

      const signature1 = await createSignature(
        [
          data1[0].toString(), // tokenID
          data1[1].toString(), // quantity
          data1[2].toString(), // sellOrderSupply
          data1[3].toString(), // sellOrderPrice
          data1[4].toString(), // enableMakeOffer
          data1[6].toString(), // tokenType
          data1[7].toString(), // partnerType
          data1[8].toString(), // partnerFee
          data1[9].toString(), // transactionType
          data1[11].toString(), // shippingFee
          data1[12].toString(), // expiredAt
          baseAddresses[0], // creator
          buyer1.address, // receiver
          ethers.ZeroAddress, // tokenAddress (native ETH)
          baseAddresses[2], // contractAddress (NFT collection)
          sellOrderSignature, // sellOrderSignature
        ],
        [
          "uint256", "uint256", "uint256", "uint256", "uint256",
          "uint256", "uint256", "uint256", "uint256",
          "uint256", "uint256",
          "address", "address", "address", "address",
          "bytes",
        ],
        signer
      );
      const addresses1 = [...baseAddresses];
      addresses1[5] = buyer1.address;

      const strs1 = ["internal-tx-1"];
      const signatures1Array = [signature1, sellOrderSignature, "0x5678"];

      // Execute first transaction (should succeed)
      const totalValue1 = calculateTotalValue(SELL_ORDER_PRICE, FIRST_BUY_AMOUNT, SHIPPING_FEE);
      await expect(
        nftExchange.connect(buyer1).buyNowNative(
          data1,
          addresses1,
          strs1,
          signatures1Array,
          { value: totalValue1 }
        )
      ).to.not.be.reverted;

      // Verify first buyer received 2 editions
      expect(await nft1155.balanceOf(buyer1.address, TOKEN_ID)).to.equal(FIRST_BUY_AMOUNT);

      // Verify sold quantities after first purchase
      expect(await nftExchange.soldQuantity(await nft1155.getAddress(), TOKEN_ID)).to.equal(FIRST_BUY_AMOUNT);
      expect(await nftExchange.soldQuantityBySaleOrder(sellOrderSignature)).to.equal(FIRST_BUY_AMOUNT);

      // Second buyer attempts to purchase 2 editions (should fail)
      const data2 = [...baseData];
      data2[1] = SECOND_BUY_AMOUNT; // quantity
      data2[5] = SECOND_BUY_AMOUNT; // buyingAmount

      const signature2 = await createSignature(
        [
          data2[0].toString(), // tokenID
          data2[1].toString(), // quantity
          data2[2].toString(), // sellOrderSupply
          data2[3].toString(), // sellOrderPrice
          data2[4].toString(), // enableMakeOffer
          data2[6].toString(), // tokenType
          data2[7].toString(), // partnerType
          data2[8].toString(), // partnerFee
          data2[9].toString(), // transactionType
          data2[11].toString(), // shippingFee
          data2[12].toString(), // expiredAt
          baseAddresses[0], // creator
          buyer2.address, // receiver
          ethers.ZeroAddress, // tokenAddress (native ETH)
          baseAddresses[2], // contractAddress (NFT collection)
          sellOrderSignature, // sellOrderSignature
        ],
        [
          "uint256", "uint256", "uint256", "uint256", "uint256",
          "uint256", "uint256", "uint256", "uint256",
          "uint256", "uint256",
          "address", "address", "address", "address",
          "bytes",
        ],
        signer
      );
      const addresses2 = [...baseAddresses];
      addresses2[5] = buyer2.address;

      const strs2 = ["internal-tx-2"];
      const signatures2Array = [signature2, sellOrderSignature, "0x5678"];

      // Execute second transaction (should fail)
      const totalValue2 = calculateTotalValue(SELL_ORDER_PRICE, SECOND_BUY_AMOUNT, SHIPPING_FEE);
      await expect(
        nftExchange.connect(buyer2).buyNowNative(
          data2,
          addresses2,
          strs2,
          signatures2Array,
          { value: totalValue2 }
        )
      ).to.be.revertedWith("Not have enough ERC155 available");

      // Verify second buyer received nothing
      expect(await nft1155.balanceOf(buyer2.address, TOKEN_ID)).to.equal(0);

      // Verify final sold quantities (should remain 2, not exceed 3)
      expect(await nftExchange.soldQuantity(await nft1155.getAddress(), TOKEN_ID)).to.equal(FIRST_BUY_AMOUNT);
      expect(await nftExchange.soldQuantityBySaleOrder(sellOrderSignature)).to.equal(FIRST_BUY_AMOUNT);

      // Verify remaining available quantity is 1
      const remainingSupply = TOTAL_SUPPLY - FIRST_BUY_AMOUNT;
      expect(remainingSupply).to.equal(1);
    });

    it("Should allow successful purchase of remaining single edition after partial fulfillment", async function () {
      const TOTAL_SUPPLY = 3;
      const FIRST_BUY_AMOUNT = 2;
      const SECOND_BUY_AMOUNT = 1; // This should succeed

      const sellOrderSignature = await createSellOrderSignature(TOKEN_ID, TOTAL_SUPPLY, SELL_ORDER_PRICE.toString(), await nft1155.getAddress());
      const { data: baseData, addresses: baseAddresses } = createBuyRequestData(
        ERC_1155,
        TOTAL_SUPPLY,
        FIRST_BUY_AMOUNT,
        await nft1155.getAddress(),
        buyer1
      );

      // First buyer purchases 2 editions
      const data1 = [...baseData];
      data1[1] = FIRST_BUY_AMOUNT;
      data1[5] = FIRST_BUY_AMOUNT;

      const signature1 = await createSignature(
        [
          data1[0].toString(), // tokenID
          data1[1].toString(), // quantity
          data1[2].toString(), // sellOrderSupply
          data1[3].toString(), // sellOrderPrice
          data1[4].toString(), // enableMakeOffer
          data1[6].toString(), // tokenType
          data1[7].toString(), // partnerType
          data1[8].toString(), // partnerFee
          data1[9].toString(), // transactionType
          data1[11].toString(), // shippingFee
          data1[12].toString(), // expiredAt
          baseAddresses[0], // creator
          buyer1.address, // receiver
          ethers.ZeroAddress, // tokenAddress (native ETH)
          baseAddresses[2], // contractAddress (NFT collection)
          sellOrderSignature, // sellOrderSignature
        ],
        [
          "uint256", "uint256", "uint256", "uint256", "uint256",
          "uint256", "uint256", "uint256", "uint256",
          "uint256", "uint256",
          "address", "address", "address", "address",
          "bytes",
        ],
        signer
      );
      const addresses1 = [...baseAddresses];
      addresses1[5] = buyer1.address;

      const totalValue1 = calculateTotalValue(SELL_ORDER_PRICE, FIRST_BUY_AMOUNT, SHIPPING_FEE);
      await nftExchange.connect(buyer1).buyNowNative(
        data1,
        addresses1,
        ["internal-tx-1"],
        [signature1, sellOrderSignature, "0x5678"],
        { value: totalValue1 }
      );

      // Second buyer purchases remaining 1 edition
      const data2 = [...baseData];
      data2[1] = SECOND_BUY_AMOUNT;
      data2[5] = SECOND_BUY_AMOUNT;

      const signature2 = await createSignature(
        [
          data2[0].toString(), // tokenID
          data2[1].toString(), // quantity
          data2[2].toString(), // sellOrderSupply
          data2[3].toString(), // sellOrderPrice
          data2[4].toString(), // enableMakeOffer
          data2[6].toString(), // tokenType
          data2[7].toString(), // partnerType
          data2[8].toString(), // partnerFee
          data2[9].toString(), // transactionType
          data2[11].toString(), // shippingFee
          data2[12].toString(), // expiredAt
          baseAddresses[0], // creator
          buyer2.address, // receiver
          ethers.ZeroAddress, // tokenAddress (native ETH)
          baseAddresses[2], // contractAddress (NFT collection)
          sellOrderSignature, // sellOrderSignature
        ],
        [
          "uint256", "uint256", "uint256", "uint256", "uint256",
          "uint256", "uint256", "uint256", "uint256",
          "uint256", "uint256",
          "address", "address", "address", "address",
          "bytes",
        ],
        signer
      );
      const addresses2 = [...baseAddresses];
      addresses2[5] = buyer2.address;

      const totalValue2 = calculateTotalValue(SELL_ORDER_PRICE, SECOND_BUY_AMOUNT, SHIPPING_FEE);
      await expect(
        nftExchange.connect(buyer2).buyNowNative(
          data2,
          addresses2,
          ["internal-tx-2"],
          [signature2, sellOrderSignature, "0x5678"],
          { value: totalValue2 }
        )
      ).to.not.be.reverted;

      // Verify both buyers received their editions
      expect(await nft1155.balanceOf(buyer1.address, TOKEN_ID)).to.equal(FIRST_BUY_AMOUNT);
      expect(await nft1155.balanceOf(buyer2.address, TOKEN_ID)).to.equal(SECOND_BUY_AMOUNT);

      // Verify total sold quantity equals total supply
      expect(await nftExchange.soldQuantity(await nft1155.getAddress(), TOKEN_ID)).to.equal(TOTAL_SUPPLY);
      expect(await nftExchange.soldQuantityBySaleOrder(sellOrderSignature)).to.equal(TOTAL_SUPPLY);
    });
  });

  describe("Edge Cases and Additional Validations", function () {
    it("Should revert when trying to buy expired NFT", async function () {
      const expiredTime = Math.floor(Date.now() / 1000) - 3600; // 1 hour ago

      const sellOrderSignature = await createSellOrderSignature(TOKEN_ID, 1, SELL_ORDER_PRICE.toString(), await nft721.getAddress());
      const { data, addresses } = createBuyRequestData(
        ERC_721,
        1,
        1,
        await nft721.getAddress(),
        buyer1
      );

      data[12] = expiredTime; // Set expired time

      const signature = await createSignature(
        [
          data[0].toString(), // tokenID
          data[1].toString(), // quantity
          data[2].toString(), // sellOrderSupply
          data[3].toString(), // sellOrderPrice
          data[4].toString(), // enableMakeOffer
          data[6].toString(), // tokenType
          data[7].toString(), // partnerType
          data[8].toString(), // partnerFee
          data[9].toString(), // transactionType
          data[11].toString(), // shippingFee
          data[12].toString(), // expiredAt
          addresses[0], // creator
          buyer1.address, // receiver
          ethers.ZeroAddress, // tokenAddress (native ETH)
          addresses[2], // contractAddress (NFT collection)
          sellOrderSignature, // sellOrderSignature
        ],
        [
          "uint256", "uint256", "uint256", "uint256", "uint256",
          "uint256", "uint256", "uint256", "uint256",
          "uint256", "uint256",
          "address", "address", "address", "address",
          "bytes",
        ],
        signer
      );
      const addresses1 = [...addresses];
      addresses1[5] = buyer1.address;

      const totalValue = calculateTotalValue(SELL_ORDER_PRICE, 1, SHIPPING_FEE);
      await expect(
        nftExchange.connect(buyer1).buyNowNative(
          data,
          addresses1,
          ["internal-tx-1"],
          [signature, sellOrderSignature, "0x5678"],
          { value: totalValue }
        )
      ).to.be.revertedWith("Request has been expired");
    });

    it("Should revert when buying amount is zero", async function () {
      const sellOrderSignature = await createSellOrderSignature(TOKEN_ID, 5, SELL_ORDER_PRICE.toString(), await nft1155.getAddress());
      const { data, addresses } = createBuyRequestData(
        ERC_1155,
        5,
        0, // Zero buying amount
        await nft1155.getAddress(),
        buyer1
      );

      const signature = await createSignature(
        [
          data[0].toString(), // tokenID
          data[1].toString(), // quantity
          data[2].toString(), // sellOrderSupply
          data[3].toString(), // sellOrderPrice
          data[4].toString(), // enableMakeOffer
          data[6].toString(), // tokenType
          data[7].toString(), // partnerType
          data[8].toString(), // partnerFee
          data[9].toString(), // transactionType
          data[11].toString(), // shippingFee
          data[12].toString(), // expiredAt
          addresses[0], // creator
          buyer1.address, // receiver
          ethers.ZeroAddress, // tokenAddress (native ETH)
          addresses[2], // contractAddress (NFT collection)
          sellOrderSignature, // sellOrderSignature
        ],
        [
          "uint256", "uint256", "uint256", "uint256", "uint256",
          "uint256", "uint256", "uint256", "uint256",
          "uint256", "uint256",
          "address", "address", "address", "address",
          "bytes",
        ],
        signer
      );
      const addresses1 = [...addresses];
      addresses1[5] = buyer1.address;

      const totalValue = calculateTotalValue(SELL_ORDER_PRICE, 0, SHIPPING_FEE); // 0 amount
      await expect(
        nftExchange.connect(buyer1).buyNowNative(
          data,
          addresses1,
          ["internal-tx-1"],
          [signature, sellOrderSignature, "0x5678"],
          { value: totalValue }
        )
      ).to.be.revertedWith("Buying amount must be greater than 0");
    });

    it("Should revert with invalid signer", async function () {
      const { data, addresses } = createBuyRequestData(
        ERC_721,
        1,
        1,
        await nft721.getAddress(),
        buyer1
      );

      // Use unauthorized signer
      const invalidSignature = await createSignature(
        [data[0].toString()],
        ["uint256"],
        buyer1 // Invalid signer
      );

      const addresses1 = [...addresses];
      addresses1[5] = buyer1.address;

      const totalValue = calculateTotalValue(SELL_ORDER_PRICE, 1, SHIPPING_FEE);
      await expect(
        nftExchange.connect(buyer1).buyNowNative(
          data,
          addresses1,
          ["internal-tx-1"],
          [invalidSignature, "0x1234", "0x5678"],
          { value: totalValue }
        )
      ).to.be.revertedWith("NFT is invalid");
    });
  });
});
